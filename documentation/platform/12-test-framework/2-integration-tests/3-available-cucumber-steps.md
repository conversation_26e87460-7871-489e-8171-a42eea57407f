PATH: XTREEM/2+Integration+tests/3+Available+Cucumber+steps

# Available Cucumber steps

Currently the following Cucumber steps are available:

- the "(.*)" notifications icon in the actions header is (displayed|hidden)
- the settings profile action in the application top bar is (displayed|hidden)
- the user clicks the settings profile action in the application top bar
- the settings profile action in the application top bar is (opened|closed)
- the user clicks the "(.*)" (bound|labelled) multi action button on (the main page|a modal|a modal header|a full width modal|the detail panel|the navigation panel)
- the user adds the lock entry "(.*)"
- the user removes the lock entry "(.*)"
- the user is logged into the system in (ultrawide desktop|HD desktop|desktop|tablet|mobile) mode using the "([^"\n\r]*)" user name and "([^"\n\r]*)" password
- the user logs out of the system
- the user opens the application on a (ultrawide desktop|HD desktop|desktop|mobile|tablet)
- the user opens the application on a (ultrawide desktop|HD desktop|desktop|mobile|tablet) using the following link: "(.*)"
- the user waits (?:for )?(\d+) (?:second|seconds)
- the test id ("[a-z0-9- ]*") text content is "([^"\n\r]*)"
- the test id ("[a-z0-9- ]*") not exists in the page
- element with (test id|class|css selector) "(.*)" looks as before
- takes a screenshot
- the page does not have any "(minor|moderate|serious|critical)" accessibility violations
- the user executes an accessibility tests scan
- the user stores the generated value with length (\b([5-9]|1[0-3])\b) with the key "(.*)"
- the accordion with title "(.*)" is (expanded|collapsed)
- the user (expands|collapses) accordion with title "(.*)" on (the main page|the detail panel|a modal| a full width modal)
- the user clicks the "([$A-Za-z0-9\s]*)" (bound|labelled) business action button on (the main page|a modal|a full width modal|a modal header|the sidebar|the mobile sidebar|the detail panel|the navigation panel)
- the "([$A-Za-z0-9\s]*)" (bound|labelled) business action button on (the main page|a modal|a modal header|a full width modal|the detail panel|the navigation panel) is (disabled|enabled|hidden|visible)
- the user clicks the (create|save|delete|cancel|duplicate) CRUD button on (the main page|a modal|a full width modal|the sidebar)
- the (create|save|delete|cancel|duplicate) CRUD button on (the main page|a modal|a full width modal|the sidebar) is (disabled|enabled|hidden|visible)
- the user clicks the "([A-Za-z0-9\s]*)" (bound|labelled) header action button on (the main page|a modal header)
- the "([A-Za-z0-9\s]*)" (bound|labelled) header action button on (the main page|a modal header) is (disabled|enabled)
- the user clicks the (cancel|select) action on the lookup dialog
- the lookup dialogs (cancel|select) action is (disabled|enabled)
- the title of the "([^"\n\r]*)" (bound|labelled) block container on (the main page|the detail list|a modal|a full width modal|the detail panel) is "(.*)"
- the "(.*)" (bound|labelled) (section) container on (the main page|a modal|a full width modal) looks like before
- the detail panel is (displayed|hidden)
- the user selects the tab ([0-9]*) in the detail panel
- the "(.*)" (bound|labelled) titled detail panel header is (displayed|hidden)
- the user selects the "(.*)" labelled tab in the detail panel
- the user clicks the detail panel closing button
- the "(.*)" (bound|labelled) block container on (the main page|a modal|a full width modal|the detail panel) is (displayed|hidden)
- (an error|an info|a success|a warn) dialog appears( on the main page| on the sidebar| on a full width modal)?
- the user clicks the "([$A-Za-z0-9\s]*)" labelled more actions button( on the main page| on the sidebar| on a full width modal)
- an info dialog disappears( on the main page| on the sidebar| on a full width modal)?
- the text in the (header|header pill label|body) of the dialog is "(.*)"( on the main page| on the sidebar| on a full width modal)?
- the text in the (header|body) of the dialog is
- the text in the body of the dialog contains "(.*)"( on the main page| on the sidebar| on a full width modal)?
- the user extracts the value from the confirmation dialog starting at (\d+) for (\d+) characters and stores it in key "(.*)"
- the text in the (header|body) of the error dialog is "(.*)"( on the main page| on the sidebar| on a full width modal)?
- the text in the body of the error dialog contains "(.*)"( on the main page| on the sidebar| on a full width modal)?
- the "(.*)" button of the dialog is (displayed|hidden)( on the main page| on the sidebar| on a full width modal)?
- the "(.*)" button of the dialog is (enabled|disabled)( on the main page| on the sidebar| on a full width modal)?
- the user clicks the Close button of the dialog( on the main page| on the sidebar| on a full width modal)?
- the user clicks the "(.*)" button of the( Message| Custom| Confirm| external link confirmation| Lookup)? dialog( on the main page| on the sidebar| on a full width modal)?
- searches for "(.*)" in the lookup dialog( on the main page| on the sidebar| on a full width modal)?
- no dialogs are displayed
- the dialog title is "(.*)"( on the main page| on the sidebar| on a full width modal)?
- the dialog subtitle is "(.*)"( on the main page| on the sidebar| on a full width modal)?
- the user clicks the Close button of the sidebar
- the user clicks the breadcrumb with text "(.*)" on a full width modal
- the user clicks the create button of the lookup dialog on the main page
- (a|no) (Message|Custom|Confirm) dialog is displayed( on the main page| on the sidebar| on a full width model)?
- the user clicks the "(.*)" sticker in the navigation bar
- the user sets the userName "(.*)" in the navigation bar
- the user selects the "(.*)" endpoint
- the "(.*)" endpoint is selected
- the user clicks the (close|arrow left) button in the navigation bar
- the (?:site|site and depositor) in the navigation bar is "(.*)"
- the help center icon is (displayed|hidden)
- the help center "(.*)" resource is (displayed|hidden)
- the help center page contains "(.*)"
- the help center page contains
- the user opens the help center "(.*)" resource
- the user opens the help center
- the user closes the help center
- the user switches to the (first|last) opened tab
- the user opens the persona
- scrolls to the "(.*)" (bound|labelled) section
- scrolls to the "(.*)" (bound|labelled) block
- scrolls to the "(.*)" (bound|labelled) (button|calendar|checkbox|date|label|numeric|progress|reference|rich text|separator|text|text area) field
- the "(.*)" menu button on the site map is (displayed|hidden)
- the user (minimizes|maximizes) the sitemap
- the "(.*)"(?: (sub))? menu item on the sitemap is (displayed|hidden)
- the user clicks the "(.*)"(?: (sub))? menu item
- the user (expands|collapses) the "(.*)" sub menu item
- the value of the toast is "(.*)"
- a toast with text "(.*)" is displayed
- a validation error message is displayed with text
- a toast containing text "(.*)" is displayed
- a validation error message is displayed containing text
- the user dismisses all the toasts
- the user dismisses the validation error message
- a (default|error|info|success|warning) toast with text "(.*)" is displayed
- a (default|error|info|success|warning) toast containing text "(.*)" is displayed
- no error toast or validation error message is displayed
- the user clicks the create button on the dashboard
- the dashboard creation dialog is displayed
- the dashboard creation dialog description is "(.*)"
- the "(.*)" button in the dashboard creation dialog is (enabled|disabled)
- the user clicks the "(.*)" button in the dashboard creation dialog
- the "(.*)" template in the dashboard creation dialog is displayed
- the user selects the template ([0-9]*) in the dashboard creation dialog
- the user selects the template with title "(.*)" in the dashboard creation dialog
- the template ([0-9]*) in the dashboard creation dialog is selected
- the template with title "(.*)" in the dashboard creation dialog is selected
- the dashboard page is displayed
- the "(.*)" titled empty dashboard is displayed
- the "(.*)" titled dashboard is displayed
- the "(.*)" subtitled empty dashboard is displayed
- the "(.*)" subtitled dashboard is displayed
- the "(.*)" subtitled blank dashboard is displayed
- the "(.*)" labelled tab in the dashboard is (displayed|hidden)
- the user selects the "(.*)" labelled tab in the dashboard
- the user clicks the "(.*)" labelled CRUD button in the dashboard action menu
- the "(.*)" titled widget in the dashboard is (displayed|hidden)
- the user clicks the edit dashboard title icon
- the user writes "(.*)" in the dashboard title
- the user selects the "(.*)" labelled tab in dashboard edit mode
- the "(.*)" titled dashboard in the dashboard editor is displayed
- the "(.*)" button in the dashboard editor footer is (enabled|disabled)
- the "(.*)" button in the dashboard editor is (enabled|disabled)
- the user clicks the "(.*)" button in the dashboard editor
- the user clicks the "(.*)" button in the dashboard editor footer
- the "(.*)" titled header in the dashboard editor navigation panel is displayed
- the "(.*)" titled category in the dashboard editor navigation panel is displayed
- the "(.*)" titled widget in the dashboard editor is displayed
- the user clicks the Add button of the "(.*)" titled widget card in the dashboard editor navigation panel
- the user selects the "(.*)" titled (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card|pie-chart) widget field in the (dashboard|dashboard editor)
- the user clicks the "(.*)" (labelled|bound) button in the dashboard editor navigation panel
- the "(.*)" titled widget editor dialog is displayed
- the value of the step title of the widget editor dialog is "(.*)"
- the value of the step subtitle of the widget editor dialog is "(.*)"
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field in the widget editor dialog
- the "(.*)" (dropdown|text) field in the widget editor dialog is (displayed|hidden)
- the "(.*)" (dropdown|text) field in the widget editor dialog is (enabled|disabled)
- the value of the "([^"\n\r]*)" (text|dropdown) field in the widget editor dialog is "(.*)"
- the user selects the "(.*)" widget card in the widget editor dialog
- the "(.*)" widget card in the widget editor dialog is (selected|unselected)
- the "(.*)" widget card in the widget editor dialog is (displayed|hidden)
- the "(.*)" button in the widget editor dialog is (displayed|hidden)
- the "(.*)" button in the widget editor dialog is (enabled|disabled)
- the user clicks the "(.*)" button in the widget editor dialog
- the user searches for "(.*)" in the widget editor dialog
- the user clears the search field in the widget editor dialog
- the user (selects|unselects) the "(.*)" tree-view element in the widget editor dialog
- the "(.*)" tree-view element in the widget editor dialog is (displayed|hidden)
- the "(.*)" tree-view element in the widget editor dialog is (checked|unchecked)
- the "(.*)" table button in the widget editor dialog is (displayed|hidden)
- the user clicks the "(.*)" table button in the widget editor dialog
- the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the widget editor dialog
- the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)
- the "([^"\n\r]*)" validation error of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)
- the "([^"\n\r]*)" date field of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" date field of row "([^"\n\r]*)" in the widget editor dialog
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog
- the value of the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog is "(.*)"
- the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)
- the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog is (enabled|disabled)
- the value of the "([^"\n\r]*)" paragraph field of row "([^"\n\r]*)" in the widget editor dialog is "(.*)"
- the user (ticks|unticks) the "(.*)" checkbox field in the widget editor dialog
- the "(.*)" preview button in the widget editor dialog is (displayed|hidden)
- the "(.*)" checkbox field in the widget editor dialog is (checked|unchecked)
- the "(.*)" more actions button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card)? widget field is (enabled|disabled)
- the "(.*)" more actions button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card)? widget field is (displayed|hidden)
- the user clicks the "(.*)"(?: more actions)? button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card)? widget field
- the value of the basic widget field contains
- the user clicks the "(.*)" button of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process)? widget field
- the "(.*)" button of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process)? widget field is (enabled|disabled)
- the "(.*)" button of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process)? widget field is (displayed|hidden)
- the user selects the card ([0-9]*) of the table widget field
- the user (ticks|unticks) the card of the table widget field
- the user clicks row actions button of the selected (card|row) of the table widget field
- the user clicks the "([^"\n\r]*)" dropdown action of the selected (card|row) of the table widget field
- the user scrolls to the (first|last) (card|row) of the table widget field
- the user scrolls down to the (card|row) with text "([^"\n\r]*)" and (column header|card section) "([^"\n\r]*)" of the table widget field
- the user (ticks|unticks) all the cards of the table widget field
- the value of the row ([0-9]*) on the (left|right) of the card of the table widget field is "(.*)"
- the user stores the value of the row ([0-9]*) on the (left|right) of the card of the table widget with key "(.*)"
- the user clicks the link of the row ([0-9]*) on the (left|right) of the card of the table widget field
- the user selects the row with text "([^"\n\r]*)" and column header "([^"\n\r]*)" of the table widget field
- the user (ticks|unticks) the row of the table widget field
- the user (ticks|unticks) all the rows of the table widget field
- the user (expands|collapses) the row of the table widget field
- the value of the cell with column header "([^"\n\r]*)" of the row of the table widget field is "(.*)"
- the user clicks the link of the cell with column header "(.*)" of the row of the table widget field
- the value of the gauge widget field is "(.*)"
- the value of the tile-indicator widget field is "(.*)"
- the number of records in the table widget fields is "(.*)"
- the total count of records in the table widget fields is "(.*)"
- the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field is (displayed|hidden)
- the user clicks the "([^"\n\r]*)" toggle button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card|pie-chart)? widget field
- the user clears the value of the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field
- the value of the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field is "([^"\n\r]*)"
- the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field
- the user (increases|decreases) the widget field by (-?\d+),(-?\d+) pixels
- the user clicks the "(previous|next)" period button in the (table) widget
- the user selects the "(.*)" period type toggle button in the (table) widget
- the user selects the "(.*)" date period in the (table) widget
- the user clicks the "(.*)" (bound|labelled) button on (the main page|a modal|a full width modal|the sidebar)
- the user switches to the "(Day|Week|Month|3 days)" view on the calendar field
- the calendar field in "(Day|Week|Month|3 days)" view is set to "(.*)"
- the user steps to "(.*)" on the calendar field
- the user navigates "(.*)" time (backward|forward) on the calendar field
- the user checks if there are events for "(.*)" on the calendar field
- the value of event number "([^"\n\r]*)" for "([^"\n\r]*)" on the calendar field is "(.*)"
- the user (ticks|unticks) the checkbox field
- the user hovers over the checkbox field
- the value of the checkbox field is "(.*)"
- the user clicks the "([^"\n\r]*)" button of the content table field
- the "([^"\n\r]*)" (dropdown|text|date) field of row "([^"\n\r]*)" in the content table field is (displayed|hidden)
- the "([^"\n\r]*)" (dropdown|text|date) field of row "([^"\n\r]*)" in the content table field is (enabled|disabled)
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the content table field
- the user clears the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the content table field
- the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the content table field is (displayed|hidden)
- the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the content table field
- the value of the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the content table field is "([^"\n\r]*)"
- the user stores the value of the date field with the key "(.*)"
- the user stores the date value of the (first|last) day of the date picker with key "(.*)"
- the user writes "(.*)" in the date field
- the value of the date field is "(.*)"
- the user writes a generated date in the date field with value "(.*)"
- the value of the date field is a generated date with value "(.*)"
- the date equal to "(.*)" is (enabled|disabled|selected|not selected|out of period)
- the dates from "(.*)" to "(.*)" are (enabled|disabled|out of period)
- the user writes a generated date with value "(.*)" from today to the selected date field
- the value of the date field is a generated date from today with value "(.*)"
- the user selects the "(.*)" month of the (start|end) date-time-range field
- the user selects the "(.*)" year of the (start|end) date-time-range field
- the user selects the "(.*)" day in the (start|end) date-time-range field
- the user writes "(.*)" in time field of the (start|end) date-time-range field
- the user clicks the "(.*)" toggle button of the (start|end) date-time-range field
- the user (ticks|unticks) the "(.*)" checkbox of the (start|end) date-time-range field
- the value of the (start|end) date-time-range field is "(.*)"
- the user leaves the focus from the (start|end) date-time-range field
- the user clicks the time zone field in the (start|end) date-time-range field
- the time-zone value in the (start|end) date-time-range field is "(.*)"
- the date-time-range field is mandatory
- the time-zone in the date-time-range field is (displayed|hidden)
- the title of the item on the row "([^"\n\r]*)" in the detail list is "([^"\n\r]*)"
- the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|text|reference|link) field of the row (\d+) in the detail list is "([^"\n\r]*)"
- the user selects the nested link field with the value "([^"\n\r]*)" of the row "([^"\n\r]*)" in the detail list
- the title of the "([^"\n\r]*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal) is "(.*)"
- the title of the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal) is (displayed|hidden)
- the helper text of the "([^"\n\r]*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is "(.*)"
- the postfix of the "([^"\n\r]*)" (bound|labelled) text field on the detail panel is "(.*)"
- the helper text of the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal) is (displayed|hidden)
- the "([^"\n\r]*)" (bound|labelled) (button|calendar|checkbox|count|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|image|label|link|multi dropdown|multi reference|numeric|node-browser-tree|progress|radio|reference|rich text|scan|separator|static-content|text|text area|time|select|switch|code editor|graphiql editor|pdf viewer|table|summary table) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (displayed|hidden)
- the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the sidebar) is (enabled|disabled)
- the "(.*)" (bound|labelled) (reference|table) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (valid|invalid)
- the user clicks in the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the detail panel|the sidebar)
- the user clicks the "([^"\n\r]*)" (bound|labelled) action of the "([^"\n\r]*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the detail panel|the sidebar)
- scrolls to the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the sidebar)
- the value of the clipboard is "(.*)"
- the user presses ((?:(?:Ctrl|Alt|ArrowDown|ArrowLeft|ArrowRight|ArrowUp|Backspace|Cancel|Clear|Comma|Control|Delete|Dot|Eight|End|Enter|Equal|Escape|F1|F10|F11|F12|F2|F3|F4|F5|F6|F7|F8|F9|Five|Four|Help|Home|Insert|Meta|Minus|Nine|One|PageDown|PageUp|Pause|Plus|Return|Semicolon|Seven|Shift|Six|Slash|Space|Star|Tab|Three|Two|Unidentified|ZenkakuHankaku|Zero|c|v)\+?)+)
- the element with the following selector is focused: "(.*)"
- the "(.*)" (bound|labelled) (button) field (appears|disappears) on (the main page|a modal|a full width modal|the sidebar|the detail panel)
- the user selects the "(.*)" (bound|labelled) (image|calendar|checkbox|code editor|count|date|date-time-range|relative date|dropdown-list|filter select|form designer|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|pod|progress|radio|reference|rich text|scan|select|static-content|switch|text|text area|time|vital pod|selection card|content table|filter editor|workflow designer) field on (the main page|a modal|a full width modal|the sidebar|the mobile sidebar|the navigation panel|the detail panel)
- the image field is (defined|undefined)
- the user adds the "(.*)" image to the image field
- the user removes the image from the image field
- the user clears the (code editor|date|dropdown-list|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|rich text|select|text) field
- the user writes "(.*)" in the (code editor|dropdown-list|filter select|graphiql editor|multi dropdown|multi reference|numeric|rich text|scan|select|text|text area) field
- the user selects "(.*)" in the (dropdown-list|filter select|multi dropdown|multi reference|reference|select) field
- the "(.*)" validation (error|warning|info) message of the (checkbox|dropdown-list|multi dropdown|multi reference|reference|select|text|text area|time) field is (displayed|hidden)
- the user clicks in the (checkbox|count|date|relative date|dropdown-list|filter select|icon|label|link|multi dropdown|multi reference|numeric|progress|reference|rich text|scan|select|separator|text) field
- the user clicks the "(.*)" (bound|labelled) action of the (checkbox|date|label|link|multi dropdown|multi reference|numeric|progress|reference|rich text|separator|text) field
- the user scrolls to the (calendar|checkbox|date|relative date|dropdown-list|filter select|label|numeric|progress|reference|multi dropdown|multi reference|rich text|select|separator|text) field
- the user blurs the (checkbox|date|numeric|rich text|text|reference) field
- the user writes "(.*)" to line ([0-9]*) of the (code editor|graphiql editor) field
- the user inserts line ([0-9]*) in the (code editor|graphiql editor) field
- the value of the (code editor|dropdown-list|relative date|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|select|static-content|text|text area) field is "(.*)"
- the (html value|value) of the code editor field (is|contains)
- the value of the text area field (is|contains)
- the suggestion on the (code editor|graphiql editor) field is "(.*)"
- the details of the "([^"\n\r]*)" suggestion on the (code editor|graphiql editor) field contain "(.*)"
- the title of the (image|calendar|checkbox|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|label|numeric|progress|radio|reference|multi dropdown|multi reference|static-content|rich text|text|text area|time|select|switch) field is "(.*)"
- the title of the (image|calendar|checkbox|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|label|numeric|progress|radio|reference|multi dropdown|multi reference|rich text|select|switch|text|text area|time) field is (displayed|hidden)
- the helper text of the (image|calendar|checkbox|count|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|label|link|numeric|progress|radio|reference|multi dropdown|multi reference|rich text|scan|select|switch|static-content|text|text area|time) field is "(.*)"
- the helper text of the (image|calendar|checkbox|date|date-time-range|dropdown-list|filter select|form designer|icon|label|numeric|progress|radio|reference|multi dropdown|multi reference|rich text|select|switch|text|text area|time) field is (displayed|hidden)
- the (image|calendar|checkbox|date|date-time-range|relative date|dropdown-list|filter select|form designer|multi dropdown|multi reference|numeric|reference|rich text|text|text area|time|select|switch) field is (enabled|disabled)
- the (image|calendar|checkbox|code editor|date|date-time-range|dropdown-list|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|rich text|select|switch|text|text area|time) field is read-only
- the (checkbox|date|relative date|numeric|rich text|text|nested grid|pod collection) field is (valid|invalid)
- the focus on the (dropdown-list|filter select|multi dropdown|multi reference|reference|select|switch) field is visible
- the placeholder of the (select) field is "(.*)"
- the (calendar|checkbox|date|relative date|label|numeric|progress|rich text|separator|text) field (appears|disappears)
- the postfix of the text field on the detail panel is "(.*)"
- the user stores the value of the (calendar|checkbox|code editor|count|dropdown-list|filter select|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|switch|text|text area) field with the key "(.*)"
- the user refreshes the screen
- the user navigates back using the browser back button
- at least the following list of options is displayed for the (dropdown-list|filter select|select|reference) field:"(.*)"
- the time elapsed in the text field is greater than "(.*)"
- the user adds the file "(.*)" to the file deposit field
- the user removes the file from the file deposit field
- the user selects the "(.*)" (bound|labelled) file deposit field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)
- the user clicks the "(.*)" file in the file deposit field
- the user clicks the "([^"\n\r]*)" button of the filter editor field
- the "([^"\n\r]*)" (dropdown|text|switch) field of row "([^"\n\r]*)" in the filter editor field is (displayed|hidden)
- the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the filter editor field is (enabled|disabled)
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the filter editor field
- the user clears the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the filter editor field
- the user clicks the "([^"\n\r]*)" switch of row "([^"\n\r]*)" in the filter editor field
- the "([^"\n\r]*)" switch of row "([^"\n\r]*)" in the filter editor field is "([^"\n\r]*)"
- the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the filter editor field is (displayed|hidden)
- the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the filter editor field
- the user clicks the lookup button of the filter select field
- the options of the filter select field include "(.*)"
- the user clicks in the (header|body|footer) of the form designer
- the user writes "(.*)" in the (header|body|footer) of the form designer
- the user selects the element on line (.*) in the (header|body|footer) of the form designer
- the user selects all the content of the (header|body|footer) of the form designer
- the user clicks the "(.*)" button in the form designer toolbar
- the user selects the "(.*)" option in the form designer toolbar
- the user inserts a table with ([0-9]*) rows and ([0-9]*) columns in the form designer
- the user sets the (font|background) color to "(.*)" in the form designer
- the user clicks the Close button of the "(.*)" panel in the form designer
- the user searches "(.*)" in the node-step-tree of the form designer on (the main page|a modal)
- the "(.*)" button in the form designer toolbar is (enabled|disabled)
- the "(.*)" panel in the form designer is (displayed|hidden)
- the title of the form designer editor dialog is "(.*)"
- the title step of the form designer editor dialog is "(.*)"
- the user clicks the "(.*)" button of the form designer editor dialog
- the user clicks the Close button of the form designer editor dialog
- the user selects the card with "(.*)" value in the selection card of the form designer editor dialog
- the card with "(.*)" value in the selection card of the form designer editor dialog is (selected|unselected)
- the "(.*)" button of the form designer editor dialog is (enabled|disabled)
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor
- the user opens the "([^"\n\r]*)" dropdown field of row "(.*)" in the form designer editor
- the user selects the "([^"\n\r]*)" option in the "([^"\n\r]*)" dropdown field of row "(.*)" in the form designer editor
- the user clears the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor
- the value of the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor is "([^"\n\r]*)"
- the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor is (enabled|disabled)
- the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor dialog is (displayed|hidden)
- the user clicks the "([^"\n\r]*)" table button in the form designer widget editor
- the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the form designer widget editor
- the "([^"\n\r]*)" action button of row "([^"\n\r]*)" the form designer widget editor is (displayed|hidden)
- the user clicks the "([^"\n\r]*)" switch of row "([^"\n\r]*)" in the form designer widget editor
- the user drags row "([^"\n\r]*)" (up|down) "([^"\n\r]*)" rows in the form designer editor
- the user selects the "(.*)" occurrence of "([^"]*)" (table|data container) in the form designer (header|body|footer)
- the user selects the cell with the "(.*)" column header of the query table (header|body|footer) in the table of the form designer
- the user selects the cell with the "([^"]*)" column header of the query table footer group number "(.*)" in the table of the form designer
- the user clicks in the selected cell of the table in the form designer
- the user clicks in the data container of the form designer
- the user clicks the insert paragraph (before|after) block of the selected (table|data container) in the form designer
- the user selects the unbreakable container in the selected cell of the table in the form designer
- the user selects the unbreakable container in the data container of the form designer
- the user clicks the insert paragraph (before|after) block of the selected unbreakable container of the form designer
- the user clicks in the selected unbreakable container of the form designer
- the user clicks the "([^"]*)" button in the formatting panel of the form designer
- the user clicks the "([^"]*)" button of the "([^"]*)" toggle button group in the formatting panel of the form designer
- the user sets the (background|border) color to "(.*)" in the formatting panel of the form designer
- the user selects the "([^"]*)" option in the "(.*)" dropdown field of the formatting panel of the form designer
- the user writes "([^"]*)" in the "(.*)" text field of the formatting panel of the form designer
- the user writes "(.*)" in the selected cell of the table in the form designer
- the value in the selected cell of the table in the form designer is "(.*)"
- the user writes "(.*)" in the selected data container of the form designer
- the value in the selected data container of the form designer is "(.*)"
- the user writes "(.*)" in the selected unbreakable container of the form designer
- the value in the selected unbreakable container of the form designer is "(.*)"
- the "([^"]*)" button in the formatting panel is (displayed|hidden)
- the user selects the node-step-tree in the form designer on (the main page|a modal)
- the user selects the tree-view element of level "([^"\n\r]*)" with text "([^"\n\r]*)" in the node-step-tree of the form designer
- the user (expands|collapses) the tree-view element in the node-step-tree of the form designer
- the user (ticks|unticks) the tree-view element in the node-step-tree of the form designer
- the user clicks the tree-view element in the node-step-tree of the form designer
- the user selects the "(.*)" (bound|labelled) tile (aggregate|calendar|checkbox|code editor|count|date|relative date|dropdown-list|filter select|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|switch|text|text area) field on (the main page|a modal|a full width modal|the detail panel|the sidebar)
- the value of the tile (aggregate|code editor|count|dropdown-list|relative date|date|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|select|text|text area) field is "(.*)"
- the "([^"\n\r]*)" (bound|labelled) tile (aggregate|calendar|checkbox|code editor|count|date|relative date|dropdown-list|filter select|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|switch|text|text area) field is (displayed|hidden)
- the user clicks in the tile (checkbox|count|date|relative date|dropdown-list|filter select|icon|label|link|multi dropdown|multi reference|numeric|progress|reference|rich text|scan|select|separator|text) field
- the tile (aggregate|calendar|checkbox|count|date|relative date|dropdown-list|filter select|multi dropdown|multi reference|numeric|reference|rich text|text|text area|select|switch) field is (enabled|disabled)
- the value of the label field is "(.*)"
- the label field is (enabled|disabled)
- at least the following list of options is displayed for the multi dropdown field: "(.*)"
- the user clicks the lookup button of the multi reference field
- at least the following list of options is displayed for the multi reference field: "(.*)"
- the value of the "([^"\n\r]*)" bound nested (numeric|text|label|reference|aggregate) field on the "([^"\n\r]*)" navigation panel's row is "(.*)"
- the user searches for "(.*)" in the navigation panel
- the user clears the search field in the navigation panel
- the search field value in the navigation panel is "(.*)"
- the user clicks the "(.*)" navigation panel's row
- the user clicks the record with the text "(.*)" in the navigation panel
- the user clicks the "(.*)" toggle button in the navigation panel
- the user removes the "(.*)" filter from the header of the navigation panel
- the user selects the "(.*)" dropdown option in the navigation panel
- the navigation panel is empty
- the user (opens|closes) the navigation panel
- the pill containing the "([^"\n\r]*)" filter with value "([^"\n\r]*)" for the "([^"\n\r]*)" column in the navigation panel is (displayed|hidden)
- the user removes the pill containing the "([^"\n\r]*)" filter with value "([^"\n\r]*)" for the "([^"\n\r]*)" column in the navigation panel
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (date|dropdown-list|numeric|reference|select|text) field of the selected (row|floating row) in the nested grid field
- the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (checkbox|date|dropdown-list|numeric|progress|reference|select|text) field of the selected row in the nested grid field with key "([^"\n\r]*)"
- the value of the "([^"\n\r]*)" (bound|labelled) nested (checkbox|date|dropdown-list|label|numeric|progress|reference|select|text) field of the selected row in the nested grid field is "(.*)"
- the user selects the "(.*)" (bound|labelled) nested grid field on (the main page|a modal|a full width modal|the helper panel|the sidebar|the navigation panel)
- the user selects row with text "([^"\n\r]*)" in column with header "([^"\n\r]*)" in the nested grid field
- the row with text "([^"\n\r]*)" in column with header "([^"\n\r]*)" in the nested grid field is (displayed|hidden)
- the user selects the row with the following content in the nested grid field
- the user (ticks|unticks) the main checkbox of the selected row in the nested grid field
- the user (expands|collapses) the selected row of the nested grid field
- the user clicks the "(.*)" action of the selected row in the nested grid field
- the user (ticks|unticks) the main checkbox of the card (\d+) in the nested grid field
- the value in the header of the mobile nested grid field is "(.*)"
- the level in the header of the mobile nested grid field is "(.*)"
- the user clicks the back arrow in the header of the mobile nested grid field
- the user clicks the card (\d+) in the nested grid field?
- the user (ticks|unticks) the main checkbox of the card with the text "(.*)" in the nested grid field
- all the cards in the nested grid field are (selected|unselected)
- the nested grid field is (empty|not empty) at level (\d+)(?: with parent id (\d+))?
- the main checkbox of the selected row in the nested grid field is (checked|unchecked)
- the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card (\d+) in the nested grid field is "(.*)"
- the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card (\d+) in the nested grid field with the key "(.*)"
- the user clicks the "([^"\n\r]*)" dropdown action of the card (\d+) in the nested grid field
- the "([^"\n\r]*)" dropdown action of the card (\d+) in the nested grid field is (enabled|disabled)
- the "([^"\n\r]*)" (bound|labelled) nested image field of the card (\d+) in the nested grid field is (defined|undefined)
- the user clicks the selected row of the nested grid field
- the user adds a new row to the nested grid field
- the user clicks the "(.*)" (bound|labelled) action of the nested grid field
- the user clicks the "(.*)" (bound|labelled) column of the nested grid field
- the user filters the "([^"]+)" (bound|labelled) column in the nested grid field with value "([^"]+)"
- the user filters the "([^"]+)" (bound|labelled) column in the nested grid field with filter "([^"]+)" and value "([^"]+)"
- the user filters the "([^"\n\r]*)" (bound|labelled) column in the nested grid field with filter type "(.*)"
- the user (opens|closes) the filter of the "(.*)" (bound|labelled) column in the nested grid field
- the user searches "(.*)" in the filter of the nested grid field
- the user (ticks|unticks) the item with text "(.*)" in the filter of the nested grid field
- the title of the nested grid field is "(.*)"
- the title of the nested grid field is (displayed|hidden)
- the helper text of the nested grid field is "(.*)"
- the helper text of the nested grid field is (displayed|hidden)
- the selected nested grid field is (displayed|hidden)
- the user selects the floating row of the selected nested grid field at level (\d+)(?: with parent id (\d+))?
- the user clicks the (next|previous) page button of the selected nested grid field at level (\d+)(?: with parent id (\d+))?
- the (First|Next|Previous|Last) page button of the nested grid field is (enabled|disabled) at level (\d+)(?: with parent id (\d+))
- the user scrolls down to the row with text "([^"\n\r]*)" and column header "([^"\n\r]*)" in the nested grid field
- the page number of the nested grid field is "(.*)" at level (\d+)(?: with parent id (\d+))
- the summary row paging information of the nested grid field is "(.*)" at level (\d+)(?: with parent id (\d+))
- the (First|Next|Previous|Last) page button of the nested grid field is (enabled|disabled)
- the page number of the nested grid field is "(.*)"
- the summary row paging information of the nested grid field is "(.*)"
- the option menu of the nested grid field is displayed
- the user clicks the option menu of the nested grid field
- the user clicks the "(.*)" value in the option menu of the nested grid field
- the user selects the "(.*)" (bound|labelled) node-browser-tree field on (the main page|a modal|a full width modal|the sidebar|the navigation panel)
- the user searches for "(.*)" in the node-browser-tree field
- the user selects the tree-view element of level "([^"\n\r]*)" with text "([^"\n\r]*)" in the node-browser-tree field
- the user (expands|collapses) the tree-view element in the node-browser-tree field
- the user (ticks|unticks) the tree-view element in the node-browser-tree field
- the node-browser-tree field is (enabled|disabled)
- the node-browser-tree field is (displayed|hidden)
- the helper text of the node-browser-tree field is (displayed|hidden)
- the helper text of the node-browser-tree field is "(.*)"
- the title of the node-browser-tree field is (displayed|hidden)
- the title of the node-browser-tree field is "(.*)"
- the node-browser-tree field is read-only
- the user clears the search field of the node-browser-tree field
- the tree-view element of the node-browser-tree field is (ticked|unticked)
- the tree-view element of the node-browser-tree field is (expanded|collapsed)
- the user selects the "(.*)" (bound|labelled) pod collection field on (the main page|a modal|a full width modal|the detail panel|the sidebar)
- the user clicks the "(.*)" button of the selected pod collection field
- the selected pod collection field is (valid|invalid)
- the selected pod collection field is (enabled|disabled|read-only)
- the title of the selected pod collection field is "(.*)"
- the helper text of the selected pod collection field is "(.*)"
- the user selects the "(.*)" (id|labelled) pod collection item of the selected pod collection field
- the user selects the "(.*)" icon of the selected pod collection item
- the selected pod collection item is hidden
- the user clicks the "(.*)" action of the selected pod collection item
- the user (selects|unselects) the main checkbox of the selected pod collection item
- the user (selects|unselects) the "(.*)" (bound|labelled) nested checkbox field of the selected pod collection item
- the user clicks the "(.*)" (bound|labelled) nested switch field of the selected pod collection item
- the "(.*)" (bound|labelled) nested checkbox field of the selected pod collection item is (selected|unselected)
- the value of the "([^"\n\r]*)" (bound|labelled) nested switch field of the selected pod collection item is set to "(.*)"
- the user clicks in the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference|reference|select|numeric|text area|text) field of the selected pod collection item
- the user clears the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item
- the value of the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference|reference|select|numeric|progress|text area|label|text) field of the selected pod collection item is "(.*)"
- the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item
- at least the following list of options is displayed "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item
- the helper text of the (pod|vital pod) field is "(.*)"
- the helper text of the (pod|vital pod) field is (displayed|hidden)
- the (pod|vital pod) field is (enabled|disabled)
- the (pod|vital pod) field is read-only
- the title of the (pod|vital pod) field is (displayed|hidden)
- the title of the (pod|vital pod) field is "(.*)"
- the user clicks the "(.*)" button of the (pod|vital pod) field
- the user clicks the "(.*)" icon of the (pod|vital pod) field
- the user clicks the "(.*)" action of the (pod|vital pod) field
- the action "(.*)" of the (pod|vital pod) field is (enabled|disabled)
- the action "(.*)" of the (pod|vital pod) field is (displayed|hidden)
- the (pod|vital pod) field is (empty|not empty)
- the (pod|vital pod) field header container value is "(.*)"
- the "(.*)" (bound|labelled) (pod|vital pod) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (displayed|hidden)
- the user clicks the "(.*)" (bound|labelled) nested switch field of the (vital pod|pod) field
- the "([^"\n\r]*)" (bound|labelled) nested switch field in the (pod|vital pod) field is set to "(.*)"
- the user clicks in the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the vital pod field
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (button|calendar|checkbox|count|date|relative date|filter select|icon|label|link|numeric|progress|radio|reference|multi reference|rich text|scan|select|table|text) field of the (pod|vital pod) field
- the user clears the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the vital pod field
- the value of the "([^"\n\r]*)" (bound|labelled) nested (button|calendar|checkbox|count|date|filter select|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|table|text|text area) field in the (pod|vital pod) field is "(.*)"
- the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (filter select|reference|multi dropdown|multi reference|select) field of the (vital pod|pod) field
- at least the following list of options is displayed "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference) field of the vital pod field
- the value of the progress field is "(.*)"
- the progress field is (enabled|disabled)
- the user selects the value "(.*)" in the radio field
- the user selects the label "(.*)" in the radio field
- the value "(.*)" of the radio field is (selected|not selected)
- the radio field is (enabled|disabled)
- the radio field is read-only
- the user writes "(.*)" in the reference field
- the user clicks the lookup button of the reference field
- the lookup button of the reference field is displayed
- a list of options is displayed for the reference field
- the reference field tunnel link is (displayed|hidden)
- the user clicks the reference field tunnel link
- the user clicks the "(.*)" action link in the reference field lookup
- the value of the rich text field is "(.*)"
- the user clicks in the "(.*)" button of the rich text field
- the user selects all the content of the rich text field
- the user selects the card with "(.*)" value in the selection card
- the card with "(.*)" value in the selection card is (selected|unselected)
- the value of the static-content field is
- the user selects the "(.*)" (bound|labelled) step-sequence field on (the main page|a modal|a full width modal|the detail panel|the sidebar)
- the title of the step-sequence field is "(.*)"
- the helper text of the step-sequence field is "(.*)"
- the title of the step-sequence field is (displayed|hidden)
- the helper text of the step-sequence field is (displayed|hidden)
- the status of the "(.*)" item of the step-sequence is (complete|current|incomplete)
- the orientation of the step-sequence field is (horizontal|vertical)
- the "(.*)" (bound|labelled) step-sequence field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (displayed|hidden)
- the user clicks in the switch field
- the user turns the switch field "(.*)"
- the switch field is set to "(.*)"
- the title help of the switch field is "(.*)"
- the size of the switch field is large
- the user selects the "([^"\n\r]*)" (bound|labelled) nested (date-time-range) field of the selected row in the table field
- the user selects the "(.*)" (month|year|day) of (start|end) nested date-time-range field of the selected row in the table field
- the user writes "(.*)" in time field of the (start|end) nested date-time-range field of the selected row in the table field
- the user clicks the "(.*)" toggle button of the (start|end) nested date-time-range field of the selected row in the table field
- the user (ticks|unticks) the "(.*)" checkbox of the (start|end) nested date-time-range field of the selected row in the table field
- the value of the (start|end|start and end) nested date-time-range field of the selected row in the table field is "(.*)"
- the user leaves the focus from the(?: start | end )?nested date-time-range field of the selected row in the table field
- the user clicks the time zone field in the (start|end) nested date-time-range field of the selected row in the table field
- the time-zone value in the (start|end) nested date-time-range field of the selected row in the table field is "(.*)"
- the user selects the row with text "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) column header of the table field
- the user selects the row with text "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) column header of the summary table field
- the user selects the (?:(?:row ([0-9]*))|(floating row)) of the table field
- the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (date|dropdown-list|numeric|reference|select|text|filter select) field of the selected row in the table field
- the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the table field is "(.*)"
- the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested field of the selected row in the table field
- the user clicks the "(.*)" (bound|labelled) nested field of the selected row in the table field
- the "([^"\n\r]*)" (bound|labelled) nested (switch) field of the selected row in the table field is set to "(.*)"
- the user opens the lookup dialog in the "(.*)" (bound|labelled) nested reference field of the selected row in the table field
- the user clicks on the tunnel link in the "([^"\n\r]*)" (bound|labelled) nested reference field of the selected row in the table field
- the user writes a generated date with value "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field
- the user writes a generated date with value "([^"\n\r]*)" from today in the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field
- the value of the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field is a generated date from today with value "(.*)"
- the "(.*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the table field contains (errors|no error)
- the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the table field with the key "(.*)"
- the value of the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field is a generated date with value "(.*)"
- the value of the "([^"\n\r]*)" (bound|labelled) nested text field of the selected row in the table field contains the pattern
- the value of the "([^"\n\r]*)" (bound|labelled) nested text field of the selected row in the table field is a non locale date with value "(.*)"
- the user selects the "(.*)" (bound|labelled) table field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)
- the user selects the "(.*)" (bound|labelled) summary table field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)
- the user (opens|closes) the filter of the "(.*)" (bound|labelled) column in the table field
- the user searches "(.*)" in the filter of the table field
- the search value of the filter in the table field is "(.*)"
- the user (ticks|unticks) the item with text "(.*)" in the filter of the table field
- the user filters the "([^"\n\r]*)" (bound|labelled) column in the table field with filter type "(.*)"
- the user filters the "([^"\n\r]*)" (bound|labelled) column in the table field with value "(.*)"
- the user filters the "([^"]+)" (bound|labelled) column in the table field with filter "([^"]+)" and value "([^"]+)"
- the user clicks the remove all filters button in the table field
- the option menu of the table field is displayed
- the user clicks the option menu of the table field
- the user clicks the "(.*)" value in the option menu of the table field
- the user clicks the "(.*)" (bound|labelled) business action button of the table field
- the user clicks the "(.*)" (bound|labelled) header action button of the table field
- the (First|Next|Previous|Last) page button of the table field is (enabled|disabled)
- the page number of the table field is "(.*)"
- the summary row paging information of the table field is "(.*)"
- the user clicks the (next|previous) page button of the table field
- the user selects the "(.*)" tab of the table field
- the "(.*)" (bound|labelled) bulk action button of the table field is (displayed|hidden)
- the user clicks the "(.*)" (bound|labelled) bulk action button of the table field
- the clear selection button on the bulk action bar of the table field is (displayed|hidden)
- the user clicks the clear selection button on the bulk action bar of the table field
- the bulk action bar of the table field is (displayed|hidden)
- the bulk action bar of the table field has ([0-9]*) items
- the user clicks the "(Calendar|Columns display|View mode|Filter)" button in the header of the table field
- the user selects the columns "(.*)" to display in the table field
- the "(.*)" (bound|labelled) column in the table field is (displayed|hidden)
- the user clicks the "(.*)" (bound|labelled) column of the table field
- the table field is (empty|not empty)
- the user adds a new table row to the table field
- the user adds a new table row to the table field using the sidebar
- the user clicks the "(.*)" (bound|labelled) button of the table field
- the user (filters|unfilters) by errors the table field
- a total of "(.*)" errors is found in the table field
- the user filters the mobile lookup table with "(.*)" query
- the user (?:un)?selects all rows of the table field
- the "(.*)" (bound|labelled) header action button of the table field is (displayed|hidden)
- the "(.*)" (bound|labelled) button of the table field is (displayed|hidden)
- the "(.*)" (bound|labelled) header action button of the table field is (enabled|disabled)
- the "(.*)" (bound|labelled) button of the table field is (enabled|disabled)
- the value of the inline action tooltip in the table field is "(.*)"
- the (group by|ungroup) option in the header menu of the "(.*)" (bound|labelled) column of the table field is (displayed|hidden)
- the user clicks the (?:(group by)(?: (year|month|day))?|(ungroup)) option in the header menu of the "(.*)" (bound|labelled) column of the table field
- the value of the option menu of the table field is "(.*)"
- the table field is (valid|invalid)
- the user clicks the "(.*)" (bound|labelled) action of the table field
- the user clicks the "(.*)" (bound|labelled) add action of the table field
- the title of the table field is (displayed|hidden)
- the helper text of the table field is "(.*)"
- the clear selected items link in the filter menu of the "(.*)" (bound|labelled) column of the table field is (enabled|disabled)
- the user clicks the clear selected items link in the filter menu of the "(.*)" (bound|labelled) column of the table field
- the floating filter value of the "([^"\n\r]*)" (bound|labelled) column of the table field is "(.*)"
- the floating filter of the "([^"\n\r]*)" (bound|labelled) column of the table field is (enabled|disabled)
- the table column configuration with name "(.*)" on the sidebar is (ticked|unticked)
- the user (ticks|unticks) the table column configuration with "(.*)" name on the sidebar
- the table column configuration on the sidebar are displayed in the following order "(.*)"
- the table column configuration with name "(.*)" on the sidebar is (locked|unlocked)
- the summary table field is (enabled|disabled)
- the title of the summary table field is (displayed|hidden)
- the title of the summary table field is "(.*)"
- the helper text of the summary table field is (displayed|hidden)
- the helper text of the summary table field is "(.*)"
- the user checks if the "(.*)" (bound|labelled) column in the table field header is focused
- the user clicks the (csv|excel) export button of the table field
- the "(.*)" (bound|labelled) column in the table field contains (errors|no error)
- the validation error tooltip containing text "(.*)" in the table field is (displayed|hidden)
- the user hovers the validation error icon of the table field
- the table has "(.*)" (errors|error)
- the table contains errors
- the user closes the global validation error panel of the table field
- the user adds a new row to the mobile table
- the view dropdown of the table field is (displayed|hidden)
- the user opens the view dropdown menu of the table field
- the value of the view dropdown of the table field is "(.*)"
- the user selects the "(.*)" option of the view dropdown of the table field
- the user clicks the "(.*)" button of the view dropdown of the table field
- the user clicks the "(.*)" (bound|labelled) multi action of the table field
- the user (?:un)?ticks the main checkbox of the selected row in the table field
- the selected row of the table field is (selected|unselected)
- the user (expands|collapses) the selected row of the table field
- the user clicks the "(.*)" dropdown action of the selected row of the table field
- the "(.*)" dropdown action of the selected row in the table field is (enabled|disabled)
- the "(.*)" dropdown action of the selected row in the table field is (displayed|hidden)
- the "(.*)" inline action button of the selected row in the table field is (displayed|hidden)
- the user hovers over the "(.*)" inline action button of the selected row in the table field
- the user clicks the "(.*)" inline action button of the selected row in the table field
- the "([^"\n\r]*)" labelled nested (?:aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text|switch)? field of the selected row in the table field is (editable|readonly)
- the user clicks the main validation error icon of the selected row in the table field
- the user opens the more actions dropdown menu of the selected row of the table field
- the dropdown action menu elements of the selected row are:
- the user clicks the "(.*)" submenu option of the more actions dropdown of the selected row in the table field
- the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the summary table field is "(.*)"
- the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the summary table field with the key "([^"\n\r]*)"
- the tunnel link in the "([^"\n\r]*)" (bound|labelled) nested reference field of the selected row in the table field is (displayed|hidden)
- the user clicks the card ([0-9]*) in the table field?
- the user (ticks|unticks) the main checkbox of the card ([0-9]*) in the table field
- the user (ticks|unticks) the main checkbox of the card with the text "(.*)" in the table field
- all the cards in the table field are (selected|unselected)
- the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card ([0-9]*) in the table field is "(.*)"
- the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card ([0-9]*) in the table field with the key "(.*)"
- the user clicks the "(.*)" inline action button of the card ([0-9]*) in the (table|nested grid) field
- the "(.*)" inline action button of the card ([0-9]*) in the table field is (displayed|hidden)
- the user clicks the "([^"\n\r]*)" dropdown action of the card ([0-9]*) in the table field
- the "([^"\n\r]*)" dropdown action of the card ([0-9]*) in the table field is (enabled|disabled)
- the "([^"\n\r]*)" (bound|labelled) nested image field of the card ([0-9]*) in the table field is (defined|undefined)
- the value of the "([^"\n\r]*)" bound nested (numeric|reference|text) field in the header card is "(.*)"
- the "(.*)" bound nested image field on the header card is (defined|undefined)
- the user writes "(.*)" in the time field
- the value of the (hours|minutes) segment of the time field is "(.*)"
- the user clicks the "(.*)" toggle button of the time field
- the value of the time field is "(.*)"
- the time field is mandatory
- the user selects the "(.*)" (bound|labelled) tree field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)
- the user selects the (?:(?:row ([0-9]*))|(floating row)) of the tree field
- the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the tree field is "(.*)"
- the user (expands|collapses) the selected row of the tree field
- the user clicks the "(.*)" button in the workflow designer field
- the user clicks the "(.*)" action in the toolbar of the workflow designer
- the user selects the "(.*)" selection card on the workflow add sidebar
- the user clicks the "(.*)" button on the workflow add sidebar
- the text content in the workflow designer field is "(.*)"
- the user selects the titled "(.*)" workflow node in the workflow designer field
- the user clicks the "(.*)" icon of the workflow node in the workflow designer field
- the user clicks the "(.*)" action of the workflow node in the workflow designer field
- the user clicks the "(.*)" (left|right) button of the workflow node in the workflow designer field
- the user writes "(.*)" GraphQL request
- the user clicks the "(.*)" button in the GraphQL page header
- the "(.*)" GraphQL response is valid
- the user selects the "(.*)" GraphQL property
- the selected GraphQL property value (is|is not|contains) "(.*)"
- the selected GraphQL property value (is|is not|contains)
- the selected GraphQL property value is (lower than|equal to|greater than) "(.*)"
- the user attaches the actual GraphQL response to allure report
- selects the "(.*)" labelled navigation anchor on (the main page|a modal|a full width modal|the sidebar)
- the "(.*)" labelled navigation anchor is selected
- the "(.*)" titled page is displayed
- the "(.*)" subtitled page is displayed
- the titled page containing "(.*)" is displayed
- the subtitled page containing "(.*)" is displayed
- the "(.*)" titled sidebar is displayed?
- the user clicks the "(.*)" icon in the header on the main page
- the "(.*)" navigation arrow button in the header on the main page is (displayed|hidden)
- the "(.*)" navigation arrow button in the header on the main page is (enabled|disabled)
- the user clicks the "(.*)" navigation arrow button in the header on the main page
- the user clicks the "(.*)" labelled button in the header
- the user clicks the 360 view switch in the header
- the 360 view switch in the header is (ON|OFF|disabled)
- the user selects the header section toggle button in the header
- the "(.*)" labelled button in the header is (enabled|disabled)
- the "(.*) labelled button in the header is (displayed|hidden)
- the user clicks the more actions button in the header
- the header actions dropdown menu elements are:
- the user clicks the "(.*)" labelled more actions button in the header
- the "(.*)" labelled more actions button in the header is (enabled|disabled)
- the "(.*)" labelled more actions button in the header is (displayed|hidden)
- the user switches language to "(.*)"
- the notification with title "(.*)" in the notification center is (displayed|hidden)
- the user clicks the notifications icon in the actions header
- the user selects the notification card with title "(.*)"
- the title of the notification card is "(.*)"
- the relative date of the notification card is "(.*)"
- the description of the notification card is "(.*)"
- the user clicks the "(.*)" notification card header action
- the user clicks the "(.*)" notification card body action
- the user reads the "(.*)" pdf file
- the user stores the file name from the preview toolbar with the key "(.*)"
- the user renames the file "(.*)" with name containing "(.*)"
- the user sets the pdf threshold to "(.*)" pt
- the user resets the pdf threshold
- the user sets the pdf vertical alignment to (top|center|bottom)
- the user resets the pdf vertical alignment
- the user verifies the pdf report contains
- the user clicks the "(.*)" action in the preview toolbar
- the user clicks the "([$A-Za-z0-9\s]*)" labelled more actions button in the sidebar header
- the "([$A-Za-z0-9\s]*)" titled mobile sidebar is (displayed|hidden)
- the user clicks the "([$A-Za-z0-9\s]*)" labelled action button in the sidebar header
- the user reads the "(.*)" (txt|csv) file
- the user sets the csv separator to "(.*)"
- the user verifies the (csv|txt) file contains
- the user renames the (csv|txt) file "(.*)" with name containing "(.*)"
- the (csv|txt) file content matches the file "(.*)"
