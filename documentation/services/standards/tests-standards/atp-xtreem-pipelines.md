PATH: XTREEM/Pipelines/ATP+XTreeM+Pipelines

# ATP / XTreeM Pipelines

## Access to ATP / XTreeM Smoke Test and Functional Test pipelines in Azure devops

- The different pipelines for x3-services & xtrem-services are located [here](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionScope=%5Catp).

![list of ATP / XTreeM pipelines](assets/images/atp_xtrem_pipelines.png)

## List of ATP / XTreeM pipelines

### ATP / XTreeM smoke test pipelines

[Smoke tests pipelines](https://confluence.sage.com/display/XTREEM/Smoke+Tests+Pipelines)

### ATP / XTreeM functional test pipelines

[Functional tests pipelines](https://confluence.sage.com/display/XTREEM/Functional+Tests+Pipelines)

## ATP / XTreeM Pipelines

### ATP / XTreeM pipelines manual execution

- The different pipelines are setup with default parameters so they can be executed automatically on daily basis or to to be reexecuted easilly and manually.
- To reexecute manually the pipeline with its default parameters:

    - Select the pipeline to execute.
    - Click run pipeline.
    - verify the branch or set the required branch.
    - Click run

### ATP / XTreeM pipeline parameters definition

- Branch:

    - Choose the branch according to the type of cluster or the type or environment you want the pipeline to be executed.
      | cluster | related branch |
      | -------------------- | ----------------- |
      | dev/cluster-ci | master |
      | dev/cluster-cu | master |
      | dev/cluster-release | release/n |
      | qa/cluster-release | release/n |
      | pp/cluster-release | release/n |
      | pp/pp-prd1 | release/n |
      | pdeu/internal | release/n |

    - test scope:

        - service: for xtrem-services.
        - x3: for x3-service (ADC).

    - Tenant provisionning method:

        - static: the tenant already exists. The tenant is decommissioned and provisioned.
        - dynamic: tenant is created dynamically and then provisioned.
        - skipped: no tenant provisioning is done. (for instance for scope=X3 or when the the provisioning is done by another pipeline).
        - Refer to [Tenant Creation / Provisioning / Decommissioning](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary).

    - SkipDecommission (for Tenant provisionning method= static):

        - true: the step to decommision the tenant is skipped.
        - false: the step to decommision the tenant is proceed.
        - This parameter can be usefull if the tenant provisioning failed and you need to relaunch the pipeline and bypass the decommisioning step.

    - skip tests execution:

        - yes: neither functional test nor smoke test are executed.
        - no: functional tests or smoke tests are executed according to the type of tests choosen by the user.

    - Authentication type:

        - unsecure:
        - sageId:
        - refer to [Type of authentication](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary).

    - TARGET_URL:

        - URL to execute the tests.
        - Not required for unsecure authentication type or when the provisioning method is dynamic.
        - Required for sageId authentication type with static provisioning method.

    - tenantId:

        - Reference of the tenant. ( This information can be verified in the cloudmanager)
            - Cloud manager for development environment: https://cloudmanager.eu.dev-sagextrem.com.
            - Cloud manager for QA environment: https://cloudmanager.na.qa-sagextrem.com.
        - Parameter only used when the scope = services.

    - tenantName:

        - Name of the tenant. (e.g smoke_test_release)
        - Not required for unsecure authentication type or when the provisioning method is dynamic.
        - Required for sageId authentication type with static provisioning method.
        - Parameter used when the scope = services.

    - endPointName1:

        - Name of the endpoint.
        - Parameter only used when the scope = x3.

    - Layer for provisioning the tenant

        - Information used when the scope = services.
        - Test layer is used for the smoke tests.
        - QA layer is used for the functional tests.

    - Type of tests to execute:

        - refer to [Type of tests](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary).

    - MAX INSTANCES:

        - Number of executors to run the test in parallel. (minValue=1, maxValue=2 for now).

    - CUCUMBER_TAGS:

        - allows to filter the tests that have a specific tag.
        - Boolean operator and parenthesis can be used: e.g: (@reference_data or @manufacturing).

    - Delete tenant:
        - Parameter used when the scope = services & provisioning method = Dynamic. (Parameter forbidden otherwise).
        - Yes: the tenant is deleted after the test have been executed.
        - No: the tenant is kept after the test have been executed.

### ATP / XTreeM pipeline variable

#### Pipeline variables

These variables are defined at pipeline level.

- loginUserName: principal user, used to execute the automated tests. (e.g. <EMAIL>)
- loginPassword: password of the principal user.
- loginUserName2: secondary user, used to execute the automated tests. (e.g. <EMAIL>)
- loginPassword2: password of the secondary user.
- loginUserName3: third user, used to execute the automated tests. (not activated yet)
- loginPassword3: password of the third user.
- sageIntacctPassword: password to connect XTreeM to sageIntacct. This information can be found in Keeper.
- sageFrp1000Password: password to connect XTreeM to sageFrp1000. This information can be found in Keeper.

- Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.

Note: For the static tenants (only decomissioned and provisionned), `sageidatp*@sage.com` has to be declared in dynamoDB.

- this operation is not required:

    - For the tenant created dynamically, as `sageidatp*@sage.com` is declared as master user in cloudmanager.
    - For `sageidatp*<EMAIL>`as those user are created dynamically by the tests using SDMO UI unterface.

For more information see: [SMDO and ADC sageidatp accounts management](https://confluence.sage.com/pages/viewpage.action?pageId=*********)

#### Pipeline Library variables

These variables are defined in azure devops library.

- Important: Those variables shouldn't be modified without consent of ATP Team.

    - [ATP](https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=251&path=ATP)
        - pipelineTimeoutMins: Maximum pipeline execution duration before returning a timeout error message.
        - timeout: Maximum time the Robot waits before returning a timeout error message.
    - [smokeTestEnvironmentVarsForDev](https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=339&path=smokeTestEnvironmentVarsForDev)
    - [smokeTestEnvironmentVarsForQa](https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=340&path=smokeTestEnvironmentVarsForQa)
    - [smokeTestEnvironmentVarsForPP](https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=342&path=smokeTestEnvironmentVarsForPP)
    - [smokeTestEnvironmentVarsForPdeu](https://sage-liveservices.visualstudio.com/X3%20XTREM/_library?itemType=VariableGroups&view=VariableGroupView&variableGroupId=364&path=smokeTestEnvironmentVarsForPdeu)

    For the SmokeTestsEnvironmentVariable, refers to the following [documentation](https://confluence.sage.com/display/XTREEM/On+demand+automated+test+on+xtreem+envs+pipeline)

### ATP / XTreeM pipeline controls

- When executing a pipeline the following controls are performed.

    | Control Name                                                | Test scope | Environment and cluster to use | Tenant provisioning method         | Authentication type | TARGET_URL    | tenantId      | tenantName    | endpointName1 | Delete tenant | Error message returned                                                              |
    | ----------------------------------------------------------- | ---------- | ------------------------------ | ---------------------------------- | ------------------- | ------------- | ------------- | ------------- | ------------- | ------------- | ----------------------------------------------------------------------------------- |
    | Control TARGET_URL & endpointName1 parameters are mandatory | X3         |                                |                                    | sageid              | none or empty |               |               | none or empty |               | Mandatory parameters: TARGET_URL and endpointName1.                                 |
    | Control tenantId parameter is mandatory                     | services   |                                | Method = static                    |                     |               | none or empty |               |               |               | Mandatory parameter: tenantId.                                                      |
    | Control unsecure authentication is allowed                  | services   |                                | Any environment different from dev | unsecure            |               |               |               |               |               | Unsecure authentication method is not allowed for QA, preprod or prod environments. |
    | Control tenant deletion is allowed                          | services   |                                | Method <> Dynamic                  |                     |               |               |               |               | Yes           | Deletion of the tenant is forbidden for static and skipped provisioning methods.    |
    | Control TARGET_URL and TenantName parameters are mandatory  | services   |                                | Method <> Dynamic                  | sageId              | none or empty |               | none or empty |               |               | Mandatory parameters: TARGET_URL and TenantName.                                    |
