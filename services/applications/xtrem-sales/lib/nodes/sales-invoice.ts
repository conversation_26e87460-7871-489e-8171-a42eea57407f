import * as xtremCommunication from '@sage/xtrem-communication';
import type { Collection, Context, Dict, Reference, TextStream, decimal } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    Decimal,
    Logger,
    NodeStatus,
    ValidationSeverity,
    asyncArray,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import { SuspendException } from '@sage/xtrem-shared';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremWorkflow from '@sage/xtrem-workflow';
import * as xtremSales from '..';
import { isPostedErrorClosed } from '../functions/sales-document-common-lib';
import {
    allocateNewSequenceNumber,
    controlPost,
    linkedShipmentsArray,
    updateHeaderNotesOnCreation,
} from '../functions/sales-invoice-lib';

const logger = Logger.getLogger(__filename, 'sales-invoice');
@decorators.subNode<SalesInvoice>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    hasAttachments: true,
    async prepareBegin() {
        await xtremSales.functions.salesDocumentTaxUpdate<
            xtremSales.nodes.SalesInvoice,
            xtremSales.classes.SalesTaxCalculator
        >(
            this,
            {
                customer: await this.billToCustomer,
                documentDate: await this.date,
                stockSite: await this.site,
            },
            await xtremSales.classes.SalesTaxCalculator.create(
                this.$.context,
                await this.site,
                await this.status,
                await this.wasTaxDataChanged,
            ),
        );
    },

    async controlBegin(cx) {
        await xtremSales.events.controlBegin.documents.checkWrongTaxType(cx, this);
        await xtremSales.events.controlBegin.invoice.checkPostedStatus(cx, this);
        await xtremSales.events.controlBegin.documents.checkDocumentStatus(cx, this);
    },

    async controlEnd(cx) {
        await xtremSales.events.controlEnd.invoice.hasAtLeastOneLine(cx, await this.lines.length);
        await xtremTax.functions.validateTaxCategoryAndPaymentTerm(cx, {
            isIntacctActivationOptionEnabled: await this.$.context.isServiceOptionEnabled(
                xtremStructure.serviceOptions.intacctActivationOption,
            ),
            taxes: await this.taxes.toArray(),
            paymentTerm: await this.paymentTerm,
        });
    },

    async saveBegin() {
        const salesInvoiceDocumentNumberGenerator = await this.getDocumentNumberGenerator();

        const isSalesInvoiceChronological = await xtremSales.functions.isChronological(
            await (
                await (
                    await this.site
                ).legalCompany
            ).legislation,
            salesInvoiceDocumentNumberGenerator,
        );

        if (isSalesInvoiceChronological) {
            await this.checkIfInvoiceDateLaterThanToday();
        }

        if (this.$.status === NodeStatus.modified) {
            const oldNode = await this.$.old;
            const oldDate = await oldNode.date;
            if ((await this.date) !== oldDate) {
                if (
                    !(await xtremMasterData.functions.sequenceNumberGeneratorLibrary.areDatesInSamePeriod(
                        await salesInvoiceDocumentNumberGenerator.sequenceNumber,
                        [await this.date, oldDate],
                    ))
                ) {
                    throw new BusinessRuleError(
                        this.$.context.localize(
                            '@sage/xtrem-sales/nodes__sales_invoice__new_invoice_date_not_in_right_period',
                            'The new invoice date must be in the same period.',
                        ),
                    );
                }
            }
        }
        await updateHeaderNotesOnCreation(this);
    },

    async controlDelete(cx): Promise<void> {
        await xtremSales.events.controlDelete.invoice.checkStatusForDeletion(cx, this);
        this.__deleteInvoice = true;
    },
    async saveEnd() {
        await this.updateRelatedSalesDocumentsStatuses();
    },
    async deleteEnd(): Promise<void> {
        await this.updateRelatedSalesDocumentsStatuses();
    },
})
export class SalesInvoice
    extends xtremMasterData.nodes.BaseDocument
    implements xtremMasterData.interfaces.Document, xtremFinanceData.interfaces.SalesFinanceDocumentWithTax
{
    __skipPrepare: boolean;

    public __deleteInvoice = false;

    canUpdateIsSent = false;

    __salesTaxCalculator: xtremSales.classes.SalesTaxCalculator;

    __salesOrders: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    __salesShipments: Dict<[xtremSales.interfaces.SalesDocumentLineUpdateInterface]> = {};

    @decorators.enumPropertyOverride<SalesInvoice, 'status'>({
        // dataType: () => xtremSales.enums.salesInvoiceStatusDataType,
    })
    override readonly status: Promise<xtremSales.enums.SalesInvoiceStatus>;

    @decorators.enumPropertyOverride<SalesInvoice, 'displayStatus'>({
        dependsOn: ['lines', 'creditStatus', 'status', 'taxCalculationStatus', 'paymentStatus'],
        async updatedValue() {
            return xtremSales.functions.SalesInvoiceLib.calculateSalesInvoiceDisplayStatus(
                await this.status,
                await this.creditStatus,
                await this.taxCalculationStatus,
                await this.paymentStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSales.enums.SalesInvoiceDisplayStatus>;

    @decorators.enumProperty<SalesInvoice, 'paymentStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.openItemStatusDataType,
        defaultValue: () => 'notPaid',
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly paymentStatus: Promise<xtremFinanceData.enums.OpenItemStatus | null>;

    /** deprecated */
    @decorators.dateProperty<SalesInvoice, 'invoiceDate'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    readonly invoiceDate: Promise<date>;

    @decorators.booleanProperty<SalesInvoice, 'enablePrintButton'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return (await xtremSales.functions.checkIfCanPrintSalesInvoice(this.$.context, this)).enablePrintButton;
        },
    })
    readonly enablePrintButton: Promise<boolean>;

    @decorators.enumProperty<SalesInvoice, 'taxEngine'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.taxEngineDataType,
        async getValue() {
            return (await (await this.site).legalCompany).taxEngine;
        },
    })
    readonly taxEngine: Promise<xtremFinanceData.enums.TaxEngine | null>;

    /** deprecated */
    @decorators.referenceProperty<SalesInvoice, 'salesSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<SalesInvoice, 'site'>({
        // isRequired: true,
        isFrozen: true,
        filters: {
            control: {
                isSales: true,
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<SalesInvoice, 'salesSiteName'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => dataTypes.name,
        dependsOn: ['site'],
        async defaultValue() {
            return (await this.site).name;
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteName: Promise<string>;

    @decorators.stringProperty<SalesInvoice, 'salesSiteTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['site'],
        async defaultValue() {
            return (await (await this.site).businessEntity).taxIdNumber;
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteTaxIdNumber: Promise<string>;

    @decorators.referenceProperty<SalesInvoice, 'salesSiteLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['site'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.site).primaryAddress;
        },
    })
    readonly salesSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesInvoice, 'salesSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['salesSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.salesSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesInvoice, 'salesSiteContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['salesSiteLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return xtremSales.functions.getBusinessEntityContact(await this.salesSiteLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly salesSiteContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<SalesInvoice, 'billToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Customer,
        isFrozen: true,
        async control(cx, val) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.site,
                await val.businessEntity,
            );
        },
    })
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.stringProperty<SalesInvoice, 'billToCustomerName'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => dataTypes.name,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity).name;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToCustomerName: Promise<string>;

    @decorators.stringProperty<SalesInvoice, 'billToCustomerTaxIdNumber'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return xtremStructure.functions.anonymizeTaxId(value);
        },
        dataType: () => xtremStructure.dataTypes.taxIdentificationDataType,
        dependsOn: ['billToCustomer'],
        async defaultValue() {
            return (await (await this.billToCustomer).businessEntity).taxIdNumber;
        },
        updatedValue: useDefaultValue,
    })
    readonly billToCustomerTaxIdNumber: Promise<string>;

    @decorators.referenceProperty<SalesInvoice, 'billToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['billToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.billToCustomer).primaryAddress;
        },
    })
    readonly billToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<SalesInvoice, 'billToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['billToLinkedAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return xtremSales.functions.getSalesAddressDetail(await this.billToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly billToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<SalesInvoice, 'billToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['billToLinkedAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return xtremSales.functions.getBusinessEntityContact(await this.billToLinkedAddress);
        },
        updatedValue: useDefaultValue,
    })
    readonly billToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referencePropertyOverride<SalesInvoice, 'currency'>({
        // isRequired: true,
        dependsOn: ['billToCustomer'],
        isFrozen: true,
        async defaultValue() {
            return (await (await this.billToCustomer)?.businessEntity)?.currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<SalesInvoice, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['billToCustomer'],
        node: () => xtremMasterData.nodes.PaymentTerm,
        async defaultValue() {
            return (await this.billToCustomer).paymentTerm;
        },
        filters: { control: { businessEntityType: { _in: ['customer', 'all'] } } },
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.referenceProperty<SalesInvoice, 'incoterm'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Incoterm,
    })
    readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.collectionPropertyOverride<SalesInvoice, 'lines'>({
        dependsOn: [
            'site',
            'billToCustomer',
            'currency',
            'billToLinkedAddress',
            'salesSiteLinkedAddress',
            'companyFxRate',
        ],
        node: () => xtremSales.nodes.SalesInvoiceLine,
    })
    override readonly lines: Collection<xtremSales.nodes.SalesInvoiceLine>;

    @decorators.decimalProperty<SalesInvoice, 'totalAmountExcludingTax'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency', { lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.calculateTaxExcludedTotalAmount();
        },
        updatedValue: useDefaultValue,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'totalTaxAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'totalTaxableAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'totalExemptAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<SalesInvoice, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        defaultValue: () => 'notDone',
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.decimalProperty<SalesInvoice, 'totalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['currency', 'totalAmountExcludingTax', 'totalTaxAmountAdjusted'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) + (await this.totalTaxAmountAdjusted),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'totalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'totalTaxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.dateProperty<SalesInvoice, 'dueDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['paymentTerm', 'date'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDueDate({
                dueDateType: await paymentTerm.dueDateType,
                days: (await paymentTerm.days) ?? 0,
                baseDate: await this.date,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly dueDate: Promise<date>;

    @decorators.decimalProperty<SalesInvoice, 'discountPaymentAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['paymentTerm', 'currency'],
        async defaultValue() {
            return (await this.paymentTerm).discountAmount;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentAmount: Promise<decimal | null>;

    @decorators.decimalProperty<SalesInvoice, 'penaltyPaymentAmount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['paymentTerm', 'currency'],
        async defaultValue() {
            return (await this.paymentTerm).penaltyAmount;
        },
        updatedValue: useDefaultValue,
    })
    readonly penaltyPaymentAmount: Promise<decimal | null>;

    @decorators.enumProperty<SalesInvoice, 'penaltyPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.discountOrPenaltyTypeDataType,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm).penaltyType;
        },
        updatedValue: useDefaultValue,
    })
    readonly penaltyPaymentType: Promise<xtremMasterData.enums.DiscountOrPenaltyType | null>;

    @decorators.enumProperty<SalesInvoice, 'discountPaymentType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.enums.discountOrPenaltyTypeDataType,
        dependsOn: ['paymentTerm'],
        async defaultValue() {
            return (await this.paymentTerm).discountType;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentType: Promise<xtremMasterData.enums.DiscountOrPenaltyType | null>;

    @decorators.enumProperty<SalesInvoice, 'creditStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.enums.salesDocumentCreditStatusDataType,
        defaultValue: () => 'notCredited',
    })
    readonly creditStatus: Promise<xtremSales.enums.SalesDocumentCreditStatus>;

    @decorators.collectionProperty<SalesInvoice, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        dependsOn: ['currency', 'status'],
        async isFrozen() {
            return (await this.status) !== 'draft' && !(await this.forceUpdateForFinance);
        },
        node: () => xtremSales.nodes.SalesInvoiceTax,
    })
    readonly taxes: Collection<xtremSales.nodes.SalesInvoiceTax>;

    @decorators.decimalProperty<SalesInvoice, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.rate : 0;
        },
        async control(cx, val) {
            await cx.error
                .withMessage('@sage/xtrem-sales/nodes__sales_invoice__fx_rate_not_found', 'No exchange rate found.')
                .if(val)
                .is.zero();
        },
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.stringProperty<SalesInvoice, 'rateDescription'>({
        isPublished: true,
        async computeValue() {
            return xtremMasterData.functions.rateDescription(
                await (
                    await this.currency
                ).id,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.companyFxRate,
                await this.companyFxRateDivisor,
            );
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.decimalProperty<SalesInvoice, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await (
                    await (
                        await (
                            await this.site
                        ).legalCompany
                    ).currency
                ).id,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.divisor : 0;
        },
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.dateProperty<SalesInvoice, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
    })
    readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<SalesInvoice, 'totalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremSales.dataTypes.salesAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'totalGrossProfit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        dependsOn: ['currency', { lines: ['grossProfitAmount'] }],
        getValue() {
            return this.lines.sum(line => line.grossProfitAmount);
        },
    })
    readonly totalGrossProfit: Promise<decimal>;

    // property needed for the accounting interface
    @decorators.datePropertyOverride<SalesInvoice, 'documentDate'>({
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.enumProperty<SalesInvoice, 'financeIntegrationApp'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationAppDataType,
        computeValue() {
            return xtremFinanceData.functions.getFinanceIntegrationAppFromOrigin(
                this.$.context,
                this._id,
                'salesInvoice',
            );
        },
    })
    readonly financeIntegrationApp: Promise<xtremFinanceData.enums.FinanceIntegrationApp | null>;

    @decorators.stringProperty<SalesInvoice, 'creationNumber'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
        dependsOn: ['number'],
        deferredDefaultValue() {
            return xtremSales.functions.getCreationNumber(this);
        },
    })
    readonly creationNumber: Promise<string>;

    @decorators.textStreamPropertyOverride<SalesInvoice, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<SalesInvoice, 'externalNote'>({
        async control(cx) {
            await xtremMasterData.events.control.externalNoteControl.externalNoteEmpty(
                await this.isExternalNote,
                await this.externalNote,
                cx,
            );
        },
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<SalesInvoice, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.collectionProperty<SalesInvoice, 'arOpenItems'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.BaseOpenItem,
        join: {
            documentType() {
                return 'salesInvoice';
            },
            documentSysId() {
                return this._id;
            },
        },
    })
    readonly arOpenItems: Collection<xtremFinanceData.nodes.BaseOpenItem>;

    @decorators.integerProperty<SalesInvoice, 'openItemSysId'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            return (await this.arOpenItems.toArray()).at(0)?._id ?? null;
        },
    })
    readonly openItemSysId: Promise<number | null>;

    @decorators.collectionProperty<SalesInvoice, 'receipts'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentDocumentLine,
        join: {
            originalNodeFactory() {
                return '#SalesInvoice';
            },
            async originalOpenItem() {
                const openItem = (await this.arOpenItems.toArray()).at(0);
                return openItem ? { _id: openItem._id } : null;
            },
        },
    })
    readonly receipts: Collection<xtremFinanceData.nodes.PaymentDocumentLine>;

    // Property to show the total amount paid on the sales invoice if the paymentTrackingOption is active (standalone).
    // The properties transactionAmountPaid, companyAmountPaid and financialSiteAmountPaid are used to store the values
    // from Intacct
    @decorators.decimalProperty<SalesInvoice, 'totalAmountPaid'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        getValue() {
            return this.arOpenItems.sum(openItem => openItem.transactionAmountPaid);
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly totalAmountPaid: Promise<decimal | null>;

    // Property to show the forced amount paid on the sales invoice if the paymentTrackingOption is active (standalone).
    @decorators.decimalProperty<SalesInvoice, 'forcedAmountPaid'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            const openItem = await this.arOpenItems.takeOne(
                async arOpenItem =>
                    (await arOpenItem.documentType) === 'salesInvoice' &&
                    (await arOpenItem.documentNumber) === (await this.number),
            );
            return (await openItem?.forcedAmountPaid) ?? null;
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly forcedAmountPaid: Promise<decimal | null>;

    @decorators.decimalProperty<SalesInvoice, 'transactionAmountPaid'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly transactionAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'companyAmountPaid'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly companyAmountPaid: Promise<decimal>;

    @decorators.decimalProperty<SalesInvoice, 'financialSiteAmountPaid'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly financialSiteAmountPaid: Promise<decimal>;

    @decorators.booleanPropertyOverride<SalesInvoice, 'isTransferHeaderNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<SalesInvoice, 'isTransferLineNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferLineNote: Promise<boolean>;

    @decorators.dateProperty<SalesInvoice, 'discountPaymentBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['paymentTerm', 'date'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDiscountPaymentBeforeDate({
                discountType: (await paymentTerm.discountFrom) ?? 'afterInvoiceDate',
                discountDate: (await paymentTerm.discountDate) ?? 0,
                baseDate: await this.date,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentBeforeDate: Promise<date | null>;

    @decorators.collectionProperty<SalesInvoice, 'financeTransactions'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType: 'salesInvoice',
        },
    })
    readonly financeTransactions: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.booleanProperty<SalesInvoice, 'isOpenItemPageOptionActive'>({
        isPublished: true,
        serviceOptions: () => [xtremStructure.serviceOptions.openItemPageOption],
        getValue() {
            return true;
        },
    })
    readonly isOpenItemPageOptionActive: Promise<boolean>;

    @decorators.collectionProperty<SalesInvoice, 'postingDetails'>({
        isPublished: true,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType() {
                return { _in: ['salesInvoice', 'arInvoice'] };
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.decimalProperty<SalesInvoice, 'netBalance'>({
        isPublished: true,
        isNullable: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            return (await this.totalAmountIncludingTax) - ((await this.totalAmountPaid) ?? 0);
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly netBalance: Promise<decimal | null>;

    private async updateRelatedSalesDocumentsStatuses(): Promise<void> {
        const salesOrdersArray = await this.$.context
            .query(xtremSales.nodes.SalesOrder, {
                filter: { _id: { _in: Object.keys(this.__salesOrders) } },
                forUpdate: true,
            })
            .toArray();
        await xtremSales.classes.BaseSalesDocumentsCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: salesOrdersArray,
            tempDict: '__salesOrders',
            statusType: 'invoiceStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesOrderLineToSalesInvoiceLine,
            enumDataType: xtremSales.enums.salesDocumentInvoiceStatusDataType,
        });

        const salesShipmentsArray = await linkedShipmentsArray(this);
        await xtremSales.classes.BaseSalesDocumentsCreator.updateRelatedSalesDocumentsStatuses({
            salesDocumentsArray: salesShipmentsArray,
            tempDict: '__salesShipments',
            statusType: 'invoiceStatus',
            classInstance: this,
            aggregateNode: xtremSales.nodes.SalesShipmentLineToSalesInvoiceLine,
            enumDataType: xtremSales.enums.salesDocumentInvoiceStatusDataType,
        });
    }

    private calculateTaxExcludedTotalAmount(): Promise<decimal> {
        return this.lines.sum(line => line.amountExcludingTax);
    }

    private async checkIfInvoiceDateLaterThanToday(): Promise<void> {
        if ((await this.date) > date.today()) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-sales/nodes__sales_invoice__future_invoice_date_not_allowed',
                    'The invoice date cannot be later than today.',
                ),
            );
        }
    }

    /**
     * This method generates credit memo for a set of sales invoices not yet credited
     * (sales invoice line creditStatus = 'notCredited').
     * @param context
     * @param salesInvoices
     */
    @decorators.mutation<typeof SalesInvoice, 'createSalesCreditMemosFromInvoices'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocuments',
                type: 'array',
                item: {
                    type: 'reference',
                    node: () => xtremSales.nodes.SalesInvoice,
                },
            },
            {
                name: 'creditMemoDate',
                type: 'date',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfCreditMemos: 'integer',
                documentsCreated: {
                    type: 'array',
                    item: { type: 'reference', node: () => xtremSales.nodes.SalesCreditMemo },
                },
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesCreditMemosFromInvoices(
        context: Context,
        salesDocuments: xtremSales.nodes.SalesInvoice[],
        creditMemoDate: date = date.today(),
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocuments.length) {
            return xtremSales.classes.SalesCreditMemosCreator.parametersAreIncorrect;
        }
        const salesCreditMemosCreator = this.instantiateSalesCreditMemosCreator(context, creditMemoDate);
        await asyncArray(salesDocuments).forEach(salesInvoice =>
            salesInvoice.lines
                .filter(async creditedLine => (await creditedLine.creditStatus) !== 'credited')
                .forEach(async line => {
                    await salesCreditMemosCreator.prepareNodeCreateData([{ salesDocumentLine: line }]);
                }),
        );
        if (Object.keys(salesCreditMemosCreator.salesOutputDocuments).length !== 0) {
            return salesCreditMemosCreator.createSalesOutputDocuments();
        }
        return xtremSales.classes.SalesCreditMemosCreator.parametersAreIncorrect;
    }

    /**
     * This method generates credit memo for a set of sales invoices not yet credited
     * (sales invoice line creditStatus = 'notCredited').
     * @param context
     * @param toInvoiceLines
     */
    @decorators.mutation<typeof SalesInvoice, 'createSalesCreditMemosFromInvoiceLines'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocumentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        salesDocumentLine: {
                            type: 'reference',
                            node: () => xtremSales.nodes.SalesInvoiceLine,
                        },
                        quantityToProcess: {
                            type: 'decimal',
                            isMandatory: false,
                        },
                    },
                },
            },
            {
                name: 'creditMemoDate',
                type: 'date',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                status: 'string',
                numberOfCreditMemos: 'integer',
                documentsCreated: 'stringArray',
                lineErrors: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            lineNumber: 'integer',
                            linePosition: 'integer',
                            message: 'string',
                        },
                    },
                },
            },
        },
        startsReadOnly: true,
    })
    static async createSalesCreditMemosFromInvoiceLines(
        context: Context,
        salesDocumentLines: [{ salesDocumentLine: xtremSales.nodes.SalesInvoiceLine; quantityToProcess?: decimal }],
        creditMemoDate: date = date.today(),
    ): Promise<xtremSales.interfaces.CreateSalesOutputDocumentsReturnValues> {
        if (!salesDocumentLines.length) {
            return xtremSales.classes.SalesCreditMemosCreator.parametersAreIncorrect;
        }
        return (
            await this.instantiateSalesCreditMemosCreator(context, creditMemoDate).prepareNodeCreateData(
                salesDocumentLines,
            )
        ).createSalesOutputDocuments();
    }

    static instantiateSalesCreditMemosCreator(
        context: Context,
        creditMemoDate: date = date.today(),
    ): xtremSales.classes.SalesCreditMemosCreator {
        return new xtremSales.classes.SalesCreditMemosCreator(context, creditMemoDate);
    }

    @decorators.mutation<typeof SalesInvoice, 'post'>({
        isPublished: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesInvoice,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: {
            type: 'reference',
            node: () => xtremSales.nodes.SalesInvoice,
        },
    })
    static async post(
        context: Context,
        invoice: xtremSales.nodes.SalesInvoice,
        isSafeToRetry = false,
    ): Promise<xtremSales.nodes.SalesInvoice> {
        const status = await invoice.status;
        const taxCalculationStatus = await invoice.taxCalculationStatus;
        const discountPaymentBeforeDate = await invoice.discountPaymentBeforeDate;
        const dueDate = await invoice.dueDate;

        // Sends the invoice to posting on salesInvoiceControlAndCreateMutationResult and controls the result
        const isControlPostValidated = controlPost(context, {
            postResult: await xtremSales.functions.FinanceIntegration.salesInvoiceControlAndCreateMutationResult(
                context,
                invoice,
            ),
            isSafeToRetry,
            status,
            taxCalculationStatus,
            discountPaymentBeforeDate,
            dueDate,
        });
        if (!isControlPostValidated) {
            return invoice;
        }

        // Set new sequence number for FR/ZA
        const finalInvoiceNumber = await allocateNewSequenceNumber(context, {
            invoice,
            status,
            taxCalculationStatus,
        });
        if (finalInvoiceNumber) {
            await invoice.$.set({ number: finalInvoiceNumber });
        }

        // send notification in order to create a staging table entry for the accounting engine
        const postingResult = await xtremSales.functions.FinanceIntegration.salesInvoiceNotification(context, invoice);

        // if no notification was sent (all amounts=0), the document is considered as posted
        await invoice.$.set({
            status: postingResult.notificationsSent ? 'inProgress' : 'posted',
            forceUpdateForFinance: true,
        });
        invoice.__skipPrepare = true;
        await invoice.$.save();
        return invoice;
    }

    // This asyncMutation is used only for workflow to post a sales invoice
    @decorators.asyncMutation<typeof SalesInvoice, 'postInvoice'>({
        isPublished: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesInvoice,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: {
            type: 'reference',
            node: () => xtremSales.nodes.SalesInvoice,
        },
        serviceOptions: () => [xtremWorkflow.serviceOptions.workflowAdvanced],
    })
    static async postInvoice(
        context: Context,
        invoice: xtremSales.nodes.SalesInvoice,
        isSafeToRetry = false,
    ): Promise<xtremSales.nodes.SalesInvoice> {
        const invoiceAfterPosting = await this.post(context, invoice, isSafeToRetry);
        // invoiceAfterPosting has the invoice after sending the notification to finance
        if ((await invoiceAfterPosting.status) === 'inProgress') throw new SuspendException();
        return invoice;
    }

    @decorators.mutation<typeof SalesInvoice, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesInvoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesInvoice,
            },
            {
                name: 'documentData',
                type: 'object',
                isMandatory: true,
                properties: {
                    header: {
                        type: 'object',
                        properties: {
                            paymentTerm: { type: 'reference', node: () => xtremMasterData.nodes.PaymentTerm },
                        },
                    },
                    lines: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                baseDocumentLineSysId: 'integer',
                                storedAttributes: 'json',
                                storedDimensions: 'json',
                                uiTaxes: 'string',
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static repost(
        context: Context,
        salesInvoice: SalesInvoice,
        documentData: xtremSales.interfaces.FinanceIntegration.SalesInvoiceCreditMemoRepost,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremSales.functions.SalesInvoiceLib.repost(context, salesInvoice, documentData);
    }

    @decorators.mutation<typeof SalesInvoice, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesInvoice,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        invoice: SalesInvoice,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
            wasSuccessful: false,
            message: '',
        };

        const salesInvoiceControlFromNotificationPayloadErrors = (
            await xtremSales.functions.FinanceIntegration.salesInvoiceControlFromNotificationPayload(context, invoice)
        ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

        if (salesInvoiceControlFromNotificationPayloadErrors.length) {
            financeIntegrationCheckResult.message = salesInvoiceControlFromNotificationPayloadErrors
                .map(message => '* '.concat(message.message))
                .join('\n');
            return financeIntegrationCheckResult;
        }

        financeIntegrationCheckResult.wasSuccessful = true;
        return financeIntegrationCheckResult;
    }

    @decorators.notificationListener<typeof SalesInvoice>({
        topic: 'SalesInvoice/accountingInterface',
        startsReadOnly: true,
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        // isJustForStatus indicates that the update of finance transaction node was already done
        const shouldUpdateSalesInvoiceStatus =
            payload.isJustForStatus ??
            (await context.runInWritableContext(writableContext => {
                return xtremFinanceData.functions.reactToFinanceIntegrationReply(writableContext, payload);
            }));

        if (shouldUpdateSalesInvoiceStatus) {
            await context.runInWritableContext(async writableContext => {
                // we need to update the status of the original Sales invoice depending on the reply from finance integration
                const invoice = await writableContext.read(
                    xtremSales.nodes.SalesInvoice,
                    { number: payload.documentNumber },
                    { forUpdate: true },
                );

                // the status of the invoice is updated depending on the calculated field financeIntegrationStatus
                const newStatus = xtremFinanceData.functions.statusMapping(
                    await invoice.financeIntegrationStatus,
                    !!payload.financeExternalIntegration,
                );

                if ((await invoice.status) !== newStatus) {
                    await invoice.$.set({ status: newStatus, forceUpdateForFinance: true }); // make sure the update of the Sales invoice will not be refused by the control in saveBegin
                    await invoice.$.save();

                    const sysNotificationState = await writableContext.tryRead(
                        xtremCommunication.nodes.SysNotificationState,
                        { notificationId: payload.batchTrackingId },
                        { forUpdate: true },
                    );
                    if (sysNotificationState) {
                        if (['posted', 'submitted'].includes(newStatus))
                            await sysNotificationState.completeWithResult({ _id: invoice._id });

                        if (newStatus === 'error')
                            await sysNotificationState.completeWithError(payload.validationMessages[0].message);
                    }
                }
            });
        }
    }

    @decorators.notificationListener<typeof SalesInvoice>({
        topic: 'SalesInvoice/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const salesInvoice = await context.read(SalesInvoice, { number: document.number });

        await SalesInvoice.resendNotificationForFinance(context, salesInvoice);
    }

    // Listener to update the payment status of a sales invoice, when the open item is paid or partially paid in finance
    @decorators.notificationListener<typeof SalesInvoice>({
        topic: 'SalesInvoice/updatePaymentStatus',
        startsReadOnly: true,
    })
    static async setSalesInvoicePaymentStatus(
        readOnlyContext: Context,
        payload: { _id: number; paymentStatus: xtremFinanceData.enums.OpenItemStatus },
    ) {
        await readOnlyContext.runInWritableContext(async context => {
            const salesInvoiceToUpdate = await context.read(SalesInvoice, { _id: payload._id }, { forUpdate: true });
            await salesInvoiceToUpdate.$.set({
                forceUpdateForFinance: true,
                paymentStatus: payload.paymentStatus,
            });
            await salesInvoiceToUpdate.$.save();
        });
    }

    // Listener to initialize the payment status of a sales invoice to notPaid, when the service option paymentTrackingOption is activated
    @decorators.notificationListener<typeof SalesInvoice>({
        topic: 'SysServiceOptionState/paymentTrackingOption/activate',
    })
    static async paymentTrackingOptionActivate(context: Context): Promise<void> {
        await context.bulkUpdate(xtremSales.nodes.SalesInvoice, {
            set: { paymentStatus: 'notPaid' },
            where: { paymentStatus: null },
        });
    }

    @decorators.query<typeof SalesInvoice, 'getDealSizeTrend'>({
        isPublished: true,
        parameters: [
            { name: 'currency', type: 'reference', node: () => xtremMasterData.nodes.Currency, isMandatory: false },
        ],
        return: {
            type: 'decimal',
        },
    })
    static async getDealSizeTrend(context: Context, currency: xtremMasterData.nodes.Currency | null): Promise<number> {
        if (currency) {
            const mtdSales = await SalesInvoice.getMtdSales(context, currency);
            return mtdSales.currentMonth - mtdSales.previousMonth;
        }
        return 0;
    }

    @decorators.query<typeof SalesInvoice, 'getYtdSales'>({
        isPublished: true,
        parameters: [
            { name: 'currency', type: 'reference', node: () => xtremMasterData.nodes.Currency, isMandatory: false },
        ],
        return: {
            type: 'decimal',
        },
    })
    static async getYtdSales(context: Context, currency: xtremMasterData.nodes.Currency | null): Promise<number> {
        let total = 0;
        if (currency) {
            const today = date.today();
            const firstDayOfYear = new Date(today.year, 0, 1);
            logger.debug(
                () =>
                    `First date of year=${firstDayOfYear}, parameter=${firstDayOfYear
                        .toISOString()
                        .slice(0, 10)
                        .replace(/-/g, '')}`,
            );
            const results = context.queryAggregate(SalesInvoice, {
                filter: {
                    date: { _gte: firstDayOfYear.toISOString().slice(0, 10).replace(/-/g, '') },
                },
                group: {
                    date: { _by: 'year' },
                    currency: { id: { _by: 'value' } },
                },
                values: {
                    totalAmountExcludingTax: { sum: true },
                },
            });
            logger.debug(() => `results=${JSON.stringify(results)}`);
            const salesResults = await results.toArray();
            const correctYear = salesResults.find(result => result.group.date.year === firstDayOfYear.getFullYear());
            if (correctYear) {
                total = await SalesInvoice.calculateMonthlyTotalInRequiredCurrency(context, salesResults, currency);
            }
            logger.debug(() => `salesResults=${JSON.stringify(salesResults)}`);
            logger.debug(() => `salesResults total=${total}`);
        }
        return total;
    }

    @decorators.query<typeof SalesInvoice, 'getMtdSales'>({
        isPublished: true,
        parameters: [
            { name: 'currency', type: 'reference', node: () => xtremMasterData.nodes.Currency, isMandatory: false },
        ],
        return: {
            type: 'object',
            properties: {
                currentMonth: 'decimal',
                previousMonth: 'decimal',
            },
        },
    })
    static async getMtdSales(
        context: Context,
        currency: xtremMasterData.nodes.Currency | null,
    ): Promise<{ currentMonth: decimal; previousMonth: decimal }> {
        if (currency) {
            let currentMonth = 0;
            let previousMonth = 0;
            const today = date.today();
            const { month } = today;
            logger.debug(() => `getMtdSales currentMonth=${month}`);
            let firstDayOfPreviousMonth = new Date();
            if (month === 1) {
                firstDayOfPreviousMonth = new Date(today.year - 1, 11, 1, 23);
            } else {
                // Need to minus 2 as Date has months from 0 - 11 whereas today().month has 1 - 12
                firstDayOfPreviousMonth = new Date(today.year, month - 2, 1, 23);
            }
            logger.debug(
                () =>
                    `getMtdSales First date of previous month=${firstDayOfPreviousMonth}, parameter=${firstDayOfPreviousMonth
                        .toISOString()
                        .slice(0, 10)
                        .replace(/-/g, '')}`,
            );
            const results = context.queryAggregate(SalesInvoice, {
                filter: {
                    date: { _gte: firstDayOfPreviousMonth.toISOString().slice(0, 10).replace(/-/g, '') },
                },
                group: {
                    date: { _by: 'month' },
                    currency: { id: { _by: 'value' } },
                },
                values: {
                    totalAmountExcludingTax: { sum: true },
                },
            });
            logger.debug(() => `getMtdSales results=${JSON.stringify(results)}`);
            const salesResults = await results.toArray();
            logger.debug(() => `getMtdResults salesResults=${JSON.stringify(salesResults)}`);

            if (salesResults.length) {
                const currentMonthGroup = SalesInvoice.filterResults(
                    salesResults,
                    new Date(today.year, today.month - 1, today.day),
                );
                const previousMonthGroup = SalesInvoice.filterResults(salesResults, firstDayOfPreviousMonth);
                logger.debug(
                    () =>
                        `getMtdSales currentMonthGroup = ${JSON.stringify(
                            currentMonthGroup,
                        )} previousMonthGroup=${JSON.stringify(previousMonthGroup)}`,
                );
                currentMonth = await SalesInvoice.calculateMonthlyTotalInRequiredCurrency(
                    context,
                    currentMonthGroup,
                    currency,
                );
                previousMonth = await SalesInvoice.calculateMonthlyTotalInRequiredCurrency(
                    context,
                    previousMonthGroup,
                    currency,
                );
                return { currentMonth, previousMonth };
            }
        }
        return { currentMonth: 0, previousMonth: 0 };
    }

    setPrintingMode() {
        this.canUpdateIsPrinted = true;
        this.canUpdateIsSent = true;
    }

    /**
     * Operation that sends an email with an invoice pdf attached to an email address.
     * @param context
     * @param invoice
     * @param contactTitle
     * @param contactLastName
     * @param contactFirstName
     * @param contactEmail
     */
    @decorators.mutation<typeof SalesInvoice, 'printSalesInvoiceAndEmail'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                node: () => SalesInvoice,
            },
            { name: 'contactTitle', type: 'string', isMandatory: true },
            { name: 'contactLastName', type: 'string', isMandatory: true },
            { name: 'contactFirstName', type: 'string', isMandatory: true },
            { name: 'contactEmail', type: 'string', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async printSalesInvoiceAndEmail(
        context: Context,
        invoice: SalesInvoice,
        contactTitle: string,
        contactLastName: string,
        contactFirstName: string,
        contactEmail: string,
    ): Promise<boolean> {
        if (!(await xtremSales.functions.SalesInvoiceLib.beforePrintAndMail(context, invoice))) {
            return false;
        }

        await xtremSales.functions.SalesInvoiceLib.printSalesDocumentAndEmail(
            context,
            invoice,
            contactTitle,
            contactLastName,
            contactFirstName,
            contactEmail,
        );
        await context.runInWritableContext(async writableContext => {
            const writableInvoice = await writableContext.read(
                xtremSales.nodes.SalesInvoice,
                { _id: invoice._id },
                { forUpdate: true },
            );
            writableInvoice.canUpdateIsPrinted = true;
            writableInvoice.canUpdateIsSent = true;
            await xtremSales.functions.SalesInvoiceLib.afterPrintAndMail(writableContext, writableInvoice, true);
        });

        return true;
    }

    /**
     * Method that calculate price determination for all lines
     * @param context
     * @param salesDocument
     * @returns boolean
     */
    @decorators.mutation<typeof SalesInvoice, 'massPriceDeterminationCalculation'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesDocument',
                type: 'reference',
                node: () => xtremSales.nodes.SalesInvoice,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: {
            type: 'boolean',
            isMandatory: true,
        },
    })
    static async massPriceDeterminationCalculation(
        context: Context,
        salesDocument: xtremSales.nodes.SalesInvoice,
    ): Promise<boolean> {
        await salesDocument.lines.forEach(async line =>
            xtremSales.functions.calculateSalesPriceDetermination(
                line as xtremSales.interfaces.SalesLineNode,
                await salesDocument.billToCustomer,
                await salesDocument.date,
                await salesDocument.site,
            ),
        );
        await salesDocument.$.save();
        return true;
    }

    private static filterResults(results: Array<xtremSales.interfaces.SalesInvoiceTotalsResult>, filterDate: Date) {
        const passedDate = filterDate.toISOString().slice(0, 7);
        return results.filter((result: xtremSales.interfaces.SalesInvoiceTotalsResult) => {
            const resultDate = result.group.date.toString().slice(0, 7);
            logger.debug(() => `getMtdSales resultDate = ${resultDate} fdocm=${passedDate}`);
            if (result.group.date.toString().slice(0, 7) === passedDate) {
                return result;
            }
            return null;
        });
    }

    private static async calculateMonthlyTotalInRequiredCurrency(
        context: Context,
        group: Array<xtremSales.interfaces.SalesInvoiceTotalsResult>,
        currency: xtremMasterData.nodes.Currency,
    ): Promise<decimal> {
        try {
            return await asyncArray(group).sum(async (result: xtremSales.interfaces.SalesInvoiceTotalsResult) => {
                if (result.group) {
                    if (result.group.currency.id === (await currency.id)) {
                        return result.values.totalAmountExcludingTax.sum;
                    }
                    return xtremMasterData.functions.convertCurrency(
                        context,
                        result.values.totalAmountExcludingTax.sum,
                        result.group.currency.id,
                        await currency.id,
                        date.today(),
                    );
                }
                return 0;
            });
        } catch (error) {
            // No exchange rate available
            return 0;
        }
    }

    private static calculateMonthlyTotalGrossInRequiredCurrency(
        context: Context,
        group: Array<xtremSales.interfaces.SalesInvoiceTotalsGrossResult>,
        currency: xtremMasterData.nodes.Currency,
    ): Promise<decimal> {
        return asyncArray(group).sum(async (result: xtremSales.interfaces.SalesInvoiceTotalsGrossResult) => {
            if (result.group) {
                if (result.group.currency.id === (await currency.id)) {
                    return result.values.totalGrossProfit.sum;
                }
                return xtremMasterData.functions.convertCurrency(
                    context,
                    result.values.totalGrossProfit.sum,
                    result.group.currency.id,
                    await currency.id,
                    date.today(),
                );
            }
            return 0;
        });
    }

    beforePrintAndMail(): Promise<boolean> {
        return xtremSales.functions.SalesInvoiceLib.beforePrintAndMail(this.$.context, this);
    }

    async afterPrintAndMail() {
        this.canUpdateIsPrinted = true;
        await xtremSales.functions.SalesInvoiceLib.afterPrintAndMail(this.$.context, this);
    }

    /**
     * Checks if the invoice can be printed
     * @returns true if the invoice can be printed
     * @throws {BusinessRuleError} if the invoice cannot be printed
     */
    @decorators.mutation<typeof xtremSales.nodes.SalesInvoice, 'beforePrint'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesInvoice,
            },
        ],
        return: 'boolean',
    })
    static async beforePrint(readonlyContext: Context, invoice: SalesInvoice): Promise<boolean> {
        const canPrintInvoice = await readonlyContext.runInWritableContext(async writableContext => {
            const writableInvoice = await writableContext.read(SalesInvoice, { _id: invoice._id }, { forUpdate: true });
            return writableInvoice.beforePrintAndMail();
        });

        if (!canPrintInvoice) {
            throw new BusinessRuleError(
                readonlyContext.localize(
                    '@sage/xtrem-sales/nodes__sales_invoice__cannot_print',
                    'The sales invoice cannot be printed.',
                ),
            );
        }

        return true;
    }

    /**
     * Updates the invoice after it has been printed
     * @param writableContext
     * @param invoice the invoice that has been printed
     * @returns
     */
    @decorators.mutation<typeof xtremSales.nodes.SalesInvoice, 'afterPrint'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                node: () => xtremSales.nodes.SalesInvoice,
            },
        ],
        return: 'boolean',
    })
    static async afterPrint(readonlyContext: Context, invoice: SalesInvoice) {
        await readonlyContext.runInWritableContext(async writableContext => {
            const writableInvoice = await writableContext.read(
                xtremSales.nodes.SalesInvoice,
                { _id: invoice._id },
                { forUpdate: true },
            );
            return writableInvoice.afterPrintAndMail();
        });
        return true;
    }

    @decorators.bulkMutation<typeof SalesInvoice, 'printBulkSalesInvoice'>({ 
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-sales/node__sales_invoice_bulk_print_report_name',
                'Sales invoice',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'salesInvoice',
                documents: reports,
                reportName,
            });
        },
    })
    static async printBulkSalesInvoice(context: Context, invoice: SalesInvoice) {
        const canPrint = await invoice.beforePrintAndMail();

        if (!canPrint) {
            return {};
        }

        const report = await xtremReporting.nodes.Report.generateReportPdf(context, 'salesInvoice', '', {
            variables: JSON.stringify({
                invoice: invoice._id,
            }),
            isBulk: true,
        });

        await context.runInWritableContext(async writableContext => {
            const writableInvoice = await writableContext.read(
                xtremSales.nodes.SalesInvoice,
                { _id: invoice._id },
                { forUpdate: true },
            );

            await writableInvoice.afterPrintAndMail();
        });

        return report;
    }

    // To use only when a sales invoice notification for finance was lost.
    // There should be no records on the accounting staging and there should be at list one record on finance transaction,
    // meaning that all was done except that the notification was lost\not recorded on the accounting staging.
    // Records on the finance transaction should be all in a 'recording' status.
    @decorators.mutation<typeof SalesInvoice, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            {
                name: 'salesInvoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SalesInvoice,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, salesInvoice: SalesInvoice): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-sales/node__sales_invoice__resend_notification_for_finance',
                'Resending finance notification for sales invoice number {{invoiceNumber}}',
                { invoiceNumber: await salesInvoice.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await salesInvoice.number,
                documentType: 'salesInvoice',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            await xtremSales.functions.FinanceIntegration.salesInvoiceNotification(context, salesInvoice);
        }
        return true;
    }

    @decorators.query<typeof SalesInvoice, 'getCustomerYtdSales'>({
        isPublished: true,
        parameters: [
            { name: 'customer', type: 'reference', node: () => xtremMasterData.nodes.Customer, isMandatory: true },
        ],
        return: {
            type: 'object',
            properties: {
                ytdTotal: 'decimal',
                currencySymbol: 'string',
                decimalDigits: 'integer',
            },
        },
    })
    static async getCustomerYtdSales(
        context: Context,
        customer: xtremMasterData.nodes.Customer,
    ): Promise<{ ytdTotal: decimal; currencySymbol: string; decimalDigits: number }> {
        let currencySymbol = '';
        let decimalDigits = 2;
        if (customer) {
            const customerCurrency = await customer.currency;
            currencySymbol = await customerCurrency.symbol;
            decimalDigits = await customerCurrency.decimalDigits;
            const today = date.today();
            const firstDayOfYear = new Date(today.year, 0, 1);
            const salesResults = context
                .queryAggregate(SalesInvoice, {
                    filter: {
                        date: { _gte: firstDayOfYear.toISOString().slice(0, 10).replace(/-/g, '') },
                        billToCustomer: customer,
                        status: 'posted',
                    },
                    group: {
                        date: { _by: 'year' },
                        currency: { id: { _by: 'value' } },
                    },
                    values: { totalAmountExcludingTax: { sum: true } },
                })
                .toArray();

            const correctYear = (await salesResults).find(
                result => result.group.date.year === firstDayOfYear.getFullYear(),
            );
            if (correctYear) {
                const ytdTotal = await SalesInvoice.calculateMonthlyTotalInRequiredCurrency(
                    context,
                    await salesResults,
                    customerCurrency,
                );
                return { ytdTotal, currencySymbol, decimalDigits };
            }
        }
        return { ytdTotal: 0, currencySymbol, decimalDigits };
    }

    @decorators.query<typeof SalesInvoice, 'getCustomerYtdGross'>({
        isPublished: true,
        parameters: [
            { name: 'customer', type: 'reference', node: () => xtremMasterData.nodes.Customer, isMandatory: true },
        ],
        return: {
            type: 'object',
            properties: {
                ytdTotal: 'decimal',
                currencySymbol: 'string',
                decimalDigits: 'integer',
            },
        },
    })
    static async getCustomerYtdGross(
        context: Context,
        customer: xtremMasterData.nodes.Customer,
    ): Promise<{ ytdTotal: decimal; currencySymbol: string; decimalDigits: number }> {
        let currencySymbol = '';
        let decimalDigits = 2;
        if (customer) {
            const customerCurrency = await customer.currency;
            currencySymbol = await customerCurrency.symbol;
            decimalDigits = await customerCurrency.decimalDigits;
            const today = date.today();
            const firstDayOfYear = new Date(today.year, 0, 1);
            const salesResults = context
                .queryAggregate(SalesInvoice, {
                    filter: {
                        date: { _gte: firstDayOfYear.toISOString().slice(0, 10).replace(/-/g, '') },
                        billToCustomer: customer,
                        status: 'posted',
                    },
                    group: {
                        date: { _by: 'year' },
                        currency: { id: { _by: 'value' } },
                    },
                    values: { totalGrossProfit: { sum: true } },
                })
                .toArray();

            const correctYear = (await salesResults).find(
                result => result.group.date.year === firstDayOfYear.getFullYear(),
            );
            if (correctYear) {
                const ytdTotal = await SalesInvoice.calculateMonthlyTotalGrossInRequiredCurrency(
                    context,
                    await salesResults,
                    customerCurrency,
                );
                return { ytdTotal, currencySymbol, decimalDigits };
            }
        }
        return { ytdTotal: 0, currencySymbol, decimalDigits };
    }
}
