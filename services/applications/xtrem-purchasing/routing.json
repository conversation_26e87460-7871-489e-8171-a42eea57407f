{"@sage/xtrem-purchasing": [{"topic": "PurchaseCreditMemo/accountingInterface", "queue": "purchasing", "sourceFileName": "purchase-credit-memo.ts"}, {"topic": "PurchaseCreditMemo/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-credit-memo.ts"}, {"topic": "PurchaseCreditMemo/resendNotificationForFinance", "queue": "purchasing", "sourceFileName": "purchase-credit-memo.ts"}, {"topic": "PurchaseCreditMemo/stock/correction/reply", "queue": "purchasing", "sourceFileName": "purchase-credit-memo.ts"}, {"topic": "PurchaseCreditMemo/updatePaymentStatus", "queue": "purchasing", "sourceFileName": "purchase-credit-memo.ts"}, {"topic": "PurchaseCreditMemoLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-credit-memo-line.ts"}, {"topic": "PurchaseCreditMemoLineDiscountCharge/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-credit-memo-line-discount-charge.ts"}, {"topic": "PurchaseInvoice/accountingInterface", "queue": "purchasing", "sourceFileName": "purchase-invoice.ts"}, {"topic": "PurchaseInvoice/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-invoice.ts"}, {"topic": "PurchaseInvoice/resendNotificationForFinance", "queue": "purchasing", "sourceFileName": "purchase-invoice.ts"}, {"topic": "PurchaseInvoice/stock/correction/reply", "queue": "purchasing", "sourceFileName": "purchase-invoice.ts"}, {"topic": "PurchaseInvoice/updatePaymentStatus", "queue": "purchasing", "sourceFileName": "purchase-invoice.ts"}, {"topic": "PurchaseInvoiceLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-invoice-line.ts"}, {"topic": "PurchaseInvoiceLineToPurchaseCreditMemoLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-invoice-line-to-purchase-credit-memo-line.ts"}, {"topic": "PurchaseOrder/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-order.ts"}, {"topic": "PurchaseOrder/createTestPurchaseOrders/start", "queue": "purchasing", "sourceFileName": "purchase-order.ts"}, {"topic": "PurchaseOrder/massApproval/start", "queue": "purchasing", "sourceFileName": "purchase-order.ts"}, {"topic": "PurchaseOrder/printBulk/start", "queue": "reporting", "sourceFileName": "purchase-order.ts"}, {"topic": "PurchaseOrderLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-order-line.ts"}, {"topic": "PurchaseOrderLineToPurchaseInvoiceLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-order-line-to-purchase-invoice-line.ts"}, {"topic": "PurchaseOrderLineToPurchaseReceiptLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-order-line-to-purchase-receipt-line.ts"}, {"topic": "PurchaseReceipt/accountingInterface", "queue": "purchasing", "sourceFileName": "purchase-receipt.ts"}, {"topic": "PurchaseReceipt/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-receipt.ts"}, {"topic": "PurchaseReceipt/resendNotificationForFinance", "queue": "purchasing", "sourceFileName": "purchase-receipt.ts"}, {"topic": "PurchaseReceipt/stock/receipt/reply", "queue": "purchasing", "sourceFileName": "purchase-receipt.ts"}, {"topic": "PurchaseReceiptLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-receipt-line.ts"}, {"topic": "PurchaseReceiptLineToPurchaseInvoiceLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-receipt-line-to-purchase-invoice-line.ts"}, {"topic": "PurchaseReceiptLineToPurchaseReturnLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-receipt-line-to-purchase-return-line.ts"}, {"topic": "PurchaseRequisition/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-requisition.ts"}, {"topic": "PurchaseRequisitionLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-requisition-line.ts"}, {"topic": "PurchaseRequisitionLineDiscountCharge/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-requisition-line-discount-charge.ts"}, {"topic": "PurchaseRequisitionLineToPurchaseOrderLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-requisition-line-to-purchase-order-line.ts"}, {"topic": "PurchaseReturn/accountingInterface", "queue": "purchasing", "sourceFileName": "purchase-return.ts"}, {"topic": "PurchaseReturn/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-return.ts"}, {"topic": "PurchaseReturn/resendNotificationForFinance", "queue": "purchasing", "sourceFileName": "purchase-return.ts"}, {"topic": "PurchaseReturn/stock/issue/reply", "queue": "purchasing", "sourceFileName": "purchase-return.ts"}, {"topic": "PurchaseReturnLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-return-line.ts"}, {"topic": "PurchaseReturnLineToPurchaseCreditMemoLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-return-line-to-purchase-credit-memo-line.ts"}, {"topic": "PurchaseReturnLineToPurchaseInvoiceLine/asyncExport/start", "queue": "import-export", "sourceFileName": "purchase-return-line-to-purchase-invoice-line.ts"}, {"topic": "SysServiceOptionState/paymentTrackingOption/activate", "queue": "purchasing", "sourceFileName": "purchase-invoice.ts"}, {"topic": "UnbilledAccountPayableInputSet/asyncExport/start", "queue": "import-export", "sourceFileName": "unbilled-account-payable-input-set.ts"}, {"topic": "UnbilledAccountPayableInputSet/unbilledAccountPayableInquiry/start", "queue": "purchasing", "sourceFileName": "unbilled-account-payable-input-set.ts"}, {"topic": "UnbilledAccountPayableResultLine/asyncExport/start", "queue": "import-export", "sourceFileName": "unbilled-account-payable-result-line.ts"}, {"topic": "WorkInProgressPurchaseOrderLine/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-purchase-order-line.ts"}, {"topic": "WorkInProgressPurchaseReceiptLine/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-purchase-receipt-line.ts"}, {"topic": "WorkInProgressPurchaseReturnLine/asyncExport/start", "queue": "import-export", "sourceFileName": "work-in-progress-purchase-return-line.ts"}]}