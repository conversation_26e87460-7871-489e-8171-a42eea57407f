import * as xtremAuthorization from '@sage/xtrem-authorization';
import type {
    AsyncArrayReader,
    Collection,
    Context,
    decimal,
    integer,
    Reference,
    TextStream,
    ValidationContext,
} from '@sage/xtrem-core';
import {
    asyncArray,
    BusinessRuleError,
    date,
    Decimal,
    decorators,
    NodeStatus,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import type * as xtremMailer from '@sage/xtrem-mailer';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremSystem from '@sage/xtrem-system';
import { isDateLaterThanToday, loggers } from '../functions';
import { changesRequestedText, getPurchaseOrderApprovalData } from '../functions/order-approval';
import {
    checkTaxCalculationStatusOfPurchaseOrder,
    checkTaxCalculationStatusOfPurchaseOrderMethod,
} from '../functions/purchase-order-lib';

import { updateChangeRequested } from '../functions/document-approval';
import * as xtremPurchasing from '../index';
import type { FilteredUsers, UserSearchCriteria } from '../shared-functions/interfaces';
import { BasePurchaseDocument } from './base-document-nodes/index'; // TODO: To confirm (We cannot use xtremPurchasing.nodes.PurchaseDocument)

// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

@decorators.subNode<PurchaseOrder>({
    extends: () => BasePurchaseDocument,
    hasAttachments: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    async prepareBegin() {
        await (
            await xtremPurchasing.classes.PurchaseOrderTaxCalculator.create(
                this.$.context,
                await this.site,
                await this.status,
                true,
            )
        ).calculateTaxData(this);
    },
    async controlBegin(cx) {
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.PurchaseOrder.checkAlreadyExistingOrderReferenceForGivenSupplier(
            this,
            cx,
        );
    },
    /**
     * Purchase order create actions :
     * - A sequence number for the order number is allocated only if it is not fed. The sequence counter to use is hard coded to 'PurchaseOrder'
     */
    async createEnd() {
        await xtremPurchasing.functions.setSiteAddress(this, await this.site);
    },
    async saveBegin() {
        await xtremPurchasing.functions.PurchaseOrderLib.updateHeaderNotesOnCreation(this);
        const documentNumberGenerator = await this.getDocumentNumberGenerator();
        await documentNumberGenerator.controls();

        // On control events this throws an error because the purchaseReceipt node is readonly
        if (['pending', 'inProgress', 'posted'].includes(await this.status)) {
            const financeIntegrationCheckResult = await xtremPurchasing.nodes.PurchaseOrder.financeIntegrationCheck(
                this.$.context,
                this,
            );
            if (financeIntegrationCheckResult.message) {
                throw new BusinessRuleError(financeIntegrationCheckResult.message);
            }
        }
        await this.$.set({
            earliestExpectedDate: await this.lines
                .map(line => line.expectedReceiptDate)
                .reduce((min, d) => (d < min ? d : min)),
        });
    },

    /**
     * Purchase order delete controls :
     * - only if status is 'Draft' or 'Ready for processing'
     */
    async controlDelete(cx: ValidationContext) {
        await xtremPurchasing.events.controls.checkStatusDeletion(this, cx);
    },
})
export class PurchaseOrder
    extends BasePurchaseDocument
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
{
    public readonly sequenceNumberId = 'PurchaseOrder';

    protected readonly sugSequenceNumberId = 'PurchaseOrderSuggestion';

    override async getDocumentNumberGenerator(): Promise<xtremMasterData.classes.DocumentNumberGenerator> {
        const sequenceNumberId = (await this.isPurchaseOrderSuggestion)
            ? this.sugSequenceNumberId
            : this.sequenceNumberId;

        return xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
            sequenceNumber: sequenceNumberId,
            nodeInstance: this,
            skipControls: true,
        });
    }

    @decorators.enumPropertyOverride<PurchaseOrder, 'status'>({
        // The instance was updated : the new status is calculated according to all status properties
        dependsOn: ['receiptStatus', { lines: ['status'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseOrderLib.computeOrderStatusFromLinesStatuses(this);
            }
            return this.status;
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<PurchaseOrder, 'displayStatus'>({
        dependsOn: ['receiptStatus', 'status', 'approvalStatus', 'taxCalculationStatus'],
        async updatedValue() {
            return xtremPurchasing.functions.PurchaseOrderLib.calculatePurchaseOrderDisplayStatus(
                await this.status,
                await this.receiptStatus,
                await this.approvalStatus,
                await this.taxCalculationStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremPurchasing.enums.PurchaseOrderDisplayStatus>;

    @decorators.booleanProperty<PurchaseOrder, 'isClosedOrReceived'>({
        async computeValue() {
            await loggers.order.debugAsync(
                async () => `status ${await (await this.$.old).status} => ${await this.status}`,
            );
            if (this.$.status === NodeStatus.added) {
                return false;
            }
            if (['pending', 'inProgress'].includes(await (await this.$.old).status)) {
                return false;
            }
            return ['closed', 'received'].includes(await this.status);
        },
    })
    readonly isClosedOrReceived: Promise<boolean>;

    @decorators.booleanProperty<PurchaseOrder, 'isPurchaseOrderSuggestion'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        dependsOn: ['status', 'receiptStatus'],
        isFrozen() {
            return this.isClosedOrReceived;
        },
    })
    readonly isPurchaseOrderSuggestion: Promise<boolean>;

    @decorators.enumProperty<PurchaseOrder, 'receiptStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremPurchasing.enums.purchaseOrderReceiptStatusDataType,
        defaultValue: 'notReceived',
        duplicatedValue: useDefaultValue,
        dependsOn: [{ lines: ['status', 'lineReceiptStatus'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseOrderLib.computeOrderReceiptStatusFromLinesReceiptStatuses(
                    this,
                );
            }
            return this.receiptStatus;
        },
    })
    readonly receiptStatus: Promise<xtremPurchasing.enums.PurchaseOrderReceiptStatus>;

    @decorators.enumPropertyOverride<PurchaseOrder, 'approvalStatus'>({
        defaultValue: 'draft',
        duplicatedValue: useDefaultValue,
        /**
         * Purchase order - approval status property :
         * - is defaulted to 'pending approval' when record is created
         * - The approval status can be changed for 'Draft' purchase order type only
         */
        async control(cx, val) {
            if (this.$.status === NodeStatus.modified && val !== (await (await this.$.old).approvalStatus)) {
                xtremPurchasing.events.controls.checkApprovalStatusDraft(await (await this.$.old).status, cx);
                await xtremPurchasing.events.controls.PurchaseOrder.checkApprovalStatus(val, this, cx);
            }
        },
    })
    override readonly approvalStatus: Promise<xtremPurchasing.enums.PurchaseDocumentApprovalStatus>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'site'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
        filters: { control: { isPurchase: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'stockSite'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'businessRelation'>({
        async control(cx, supplier) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.site,
                await supplier.businessEntity,
            );
        },
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<PurchaseOrder, 'supplierLinkedAddress'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['businessRelation'],
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.businessRelation).businessEntity)._id;
                },
            },
        },
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        async defaultValue() {
            return (await this.businessRelation).primaryAddress;
        },
        // TODO: uncomment (disabled for now due to code smell raised for 'as any')
        // updatedValue() {
        //     // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        //     return xtremPurchasing.functions.getDefaultSupplierAddress(this.supplier) as any; // Hack for vital non nullable reference
        // },
    })
    readonly supplierLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<PurchaseOrder, 'supplierAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['supplierLinkedAddress'],
        async defaultValue() {
            return excludeFromUpgrade(await (await this.supplierLinkedAddress).address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplierAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseOrder, 'supplierContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['supplierLinkedAddress'],
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.supplierLinkedAddress).primaryContact)?.contact) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplierContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'billBySupplier'>({
        dependsOn: ['businessRelation'],
        defaultValue() {
            return this.businessRelation;
        },
    })
    override readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>; // | null;

    @decorators.referencePropertyOverride<PurchaseOrder, 'currency'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'transactionCurrency'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'companyCurrency'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.datePropertyOverride<PurchaseOrder, 'date'>({
        dependsOn: ['orderDate'],
        defaultValue() {
            return this.orderDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly date: Promise<date>;

    @decorators.dateProperty<PurchaseOrder, 'orderDate'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => date.today(),
        duplicatedValue: useDefaultValue,
        duplicateRequiresPrompt: true,
        lookupAccess: true,
        async control(cx, val) {
            if (
                isDateLaterThanToday(
                    this.$.status,
                    val,
                    this.$.status === NodeStatus.modified ? await (await this.$.old).orderDate : undefined,
                )
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/pages__purchase_order__order_date_cannot_be_future',
                    'The order date cannot be later than today.',
                );
            }
        },
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    readonly orderDate: Promise<date>;

    @decorators.dateProperty<PurchaseOrder, 'earliestExpectedDate'>({
        isPublished: true,
        dependsOn: ['lines'],
        isStored: true,
        isNullable: true,
    })
    readonly earliestExpectedDate: Promise<date | null>;

    @decorators.datePropertyOverride<PurchaseOrder, 'documentDate'>({
        dependsOn: ['orderDate'],
        getValue() {
            return this.orderDate;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.datePropertyOverride<PurchaseOrder, 'fxRateDate'>({
        defaultValue() {
            return this.documentDate;
        },
    })
    override readonly fxRateDate: Promise<date>;

    @decorators.referencePropertyOverride<PurchaseOrder, 'paymentTerm'>({
        dependsOn: ['status'],
        isFrozen() {
            return this.isClosedOrReceived;
        },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.textStreamProperty<PurchaseOrder, 'changeRequestedDescription'>({
        isStored: true,
        isPublished: true,
    })
    readonly changeRequestedDescription: Promise<TextStream>;

    @decorators.decimalPropertyOverride<PurchaseOrder, 'totalAmountExcludingTax'>({
        dependsOn: ['status', 'currency', { lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTax);
        },
        updatedValue: useDefaultValue,
        isFrozen() {
            return this.isClosedOrReceived;
        },
    })
    override readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseOrder, 'totalAmountExcludingTaxInCompanyCurrency'>({
        dependsOn: [{ lines: ['amountExcludingTaxInCompanyCurrency'] }], // ,'status', 'receiptStatus'],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency);
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    // TODO: uncomment from base purchase document and override here, once the platform enhancement https://jira.sage.com/browse/XT-22884 is implemented (override compute value)
    @decorators.decimalProperty<PurchaseOrder, 'totalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await xtremPurchasing.functions.BaseDocument.getCurrencyDecimalDigits(this),
            );
        },
    })
    readonly totalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.stringProperty<PurchaseOrder, 'supplierOrderReference'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        defaultValue: '',
    })
    readonly supplierOrderReference: Promise<string>;

    @decorators.collectionPropertyOverride<PurchaseOrder, 'lines'>({
        node: () => xtremPurchasing.nodes.PurchaseOrderLine,
        dependsOn: ['approvalStatus', 'isApprovalManaged'],
    })
    override readonly lines: Collection<xtremPurchasing.nodes.PurchaseOrderLine>;

    get landedCostAssignableLines() {
        return this.lines;
    }

    @decorators.enumPropertyOverride<PurchaseOrder, 'invoiceStatus'>({
        dependsOn: [{ lines: ['lineInvoiceStatus'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseOrderLib.computeOrderInvoiceStatusFromLinesInvoiceStatuses(
                    this,
                );
            }
            return this.invoiceStatus;
        },
    })
    override readonly invoiceStatus: Promise<xtremPurchasing.enums.PurchaseOrderInvoiceStatus>;

    @decorators.booleanProperty<PurchaseOrder, 'isOrderAssignmentLinked'>({
        isPublished: true,
        defaultValue: false,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        computeValue() {
            return this.lines.some(async line => (await line.uDemandOrderLine) !== null);
        },
    })
    readonly isOrderAssignmentLinked: Promise<boolean>;

    @decorators.referenceProperty<PurchaseOrder, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.DeliveryMode,
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).deliveryMode;
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode | null>;

    @decorators.referenceProperty<PurchaseOrder, 'defaultBuyer'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.User,
        dependsOn: ['businessRelation'],
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
        async defaultValue() {
            return (
                (await (await this.businessRelation).defaultBuyer) ||
                ((await this.$.context.transactionUser) as unknown as xtremSystem.nodes.User)
            );
        },
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly defaultBuyer: Reference<xtremSystem.nodes.User>;

    @decorators.booleanProperty<PurchaseOrder, 'isApprovalManaged'>({
        isPublished: true,
        async getValue() {
            return (await this.site).isPurchaseOrderApprovalManaged;
        },
    })
    readonly isApprovalManaged: Promise<boolean>;

    @decorators.decimalProperty<PurchaseOrder, 'totalQuantityToReceiveInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ lines: ['quantityToReceiveInStockUnit'] }],
        getValue() {
            return this.lines.sum(line => line.quantityToReceiveInStockUnit);
        },
    })
    readonly totalQuantityToReceiveInStockUnit: Promise<decimal>;

    @decorators.booleanProperty<PurchaseOrder, 'isGrossPriceMissing'>({
        isPublished: true,
        getValue() {
            return this.lines.some(async line => (await line.grossPrice) === 0);
        },
    })
    readonly isGrossPriceMissing: Promise<boolean>;

    // The usage of a computeValue instead of a query allows to use directly "this.lines"
    // instead of having to read the specific document first => This property can be copy/paste as is.
    @decorators.jsonProperty<PurchaseOrder, 'jsonAggregateLandedCostTypes'>({
        isPublished: true,
        dependsOn: [{ lines: ['landedCostLines'] }],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        computeValue() {
            return xtremLandedCost.functions.landedCostLineLib.getLandedCostsPerType(this.lines);
        },
    })
    readonly jsonAggregateLandedCostTypes: Promise<xtremLandedCost.interfaces.JsonAggregateLandedCostTypes>;

    /**
     * Operation that send an approval email to a user pre-filled email address.
     * The email is send only if the status is 'Draft' and the approval status is 'not submitted', 'change requested' or 'not submitted'
     * @param context
     * @param purchaseOrderNumber
     * @param url
     * @param email
     */
    @decorators.bulkMutation<typeof PurchaseOrder, 'massApproval'>({
        isPublished: true,
    })
    static async massApproval(context: Context, purchaseOrder: PurchaseOrder): Promise<void> {
        await checkTaxCalculationStatusOfPurchaseOrderMethod(purchaseOrder);
        await xtremPurchasing.functions.PurchaseOrderLib.managePurchaseOrderStatusApproval(context, {
            document: purchaseOrder,
            isApproved: true,
        });
    }

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringPropertyOverride<PurchaseOrder, 'page'>({
        getValue: () => '@sage/xtrem-purchasing/PurchaseOrder',
    })
    override readonly page: Promise<string>;

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringPropertyOverride<PurchaseOrder, 'approvalUrl'>({
        getValue: () => '@sage/xtrem-purchasing/PurchaseOrderApproval',
    })
    override readonly approvalUrl: Promise<string>;

    override async beforeSendApprovalRequestMail(
        context: Context,
        user: xtremSystem.nodes.User,
    ): Promise<xtremMasterData.interfaces.ApprovalRequestMail> {
        await checkTaxCalculationStatusOfPurchaseOrderMethod(this);
        const { subject, data } = await getPurchaseOrderApprovalData(context, this);
        return {
            subject,
            template: 'purchase_order_approval_mail',
            mailerUser: user,
            data,
        };
    }

    async afterSendApprovalRequestMail(): Promise<void> {
        await Promise.resolve(this._id);
    }

    async beforeSendRequestChangesMail(user: xtremSystem.nodes.User) {
        const { data } = await getPurchaseOrderApprovalData(this.$.context, this);
        return {
            subject: await changesRequestedText(this),
            template: 'purchase_order_request_changes_mail',
            mailerUser: user,
            data,
        };
    }

    /**
     * Operation that send a request for changes email to a user pre-filled email address.
     * The email is send only if the status is 'Draft' and the approval status is 'pending approval', 'change requested' or 'not submitted'
     * @param context
     * @param purchaseOrderNumber
     * @param url
     * @param email
     */
    @decorators.mutation<typeof PurchaseOrder, 'sendRequestChangesMail'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => PurchaseOrder, isWritable: true },
            {
                name: 'user',
                type: 'instance',
                node: () => xtremSystem.nodes.User,
                isTransientInput: true,
            },
        ],
        return: 'boolean',
    })
    static async sendRequestChangesMail(
        context: Context,
        document: PurchaseOrder,
        user: xtremSystem.nodes.User,
    ): Promise<boolean> {
        const { subject, template, data } = await document.beforeSendRequestChangesMail(user);
        await updateChangeRequested(document);
        await xtremMasterData.functions.sendMail(
            context,
            { mailerUser: user, mail: { data, subject, template } },
            document,
        );

        return true;
    }

    /**
     * Method that approve or reject the purchase order
     * @param context
     * @param purchaseOrderNumber
     */
    @decorators.mutation<typeof PurchaseOrder, 'approve'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => PurchaseOrder, isWritable: true, isMandatory: true },
            { name: 'isApproved', type: 'boolean', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async approve(context: Context, document: PurchaseOrder, isApproved: boolean): Promise<boolean> {
        if (!document) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_order__document_not_found',
                    'Document not found.',
                ),
            );
        }
        if (isApproved) {
            await checkTaxCalculationStatusOfPurchaseOrder(context, { number: await document.number });
        }

        await xtremPurchasing.functions.PurchaseOrderLib.managePurchaseOrderStatusApproval(context, {
            document,
            isApproved,
        });
        return true;
    }

    /**
     * Method that confirms the purchase order
     * @param context
     * @param purchaseOrderNumber
     */
    @decorators.mutation<typeof PurchaseOrder, 'confirm'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => PurchaseOrder, isWritable: true },
            { name: 'isConfirmed', type: 'boolean', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async confirm(context: Context, document: PurchaseOrder, isConfirmed: boolean): Promise<boolean> {
        if (isConfirmed) {
            await checkTaxCalculationStatusOfPurchaseOrder(context, { number: await document.number });
        }

        if (
            await xtremPurchasing.functions.PurchaseOrderLib.managePurchaseOrderStatusConfirmed(context, {
                document,
                isConfirm: isConfirmed,
            })
        ) {
            return true;
        }

        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_order__invalid_confirm_status',
                'Invalid status for confirmation.',
            ),
        );
    }

    /**
     * Method that creates purchase receipts for a given purchase order
     * The purchase order cannot be 'Closed', 'Fully completed' or 'Fully received'
     * All the lines have to have an item
     * @param context
     * @param purchaseOrderNumber
     */
    @decorators.mutation<typeof PurchaseOrder, 'createPurchaseReceipt'>({
        isPublished: true,
        parameters: [{ name: 'document', type: 'reference', node: () => PurchaseOrder, isMandatory: true }],
        return: { type: 'array', item: { type: 'reference', node: () => xtremPurchasing.nodes.PurchaseReceipt } },
    })
    static async createPurchaseReceipt(
        context: Context,
        document: PurchaseOrder,
    ): Promise<xtremPurchasing.nodes.PurchaseReceipt[]> {
        if (!document) {
            return [];
        }

        if (await document.lines.some(async line => (await (await line.item).status) !== 'active')) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_order__cannot_create_receipt',
                    'You need to remove the inactive items before you change the document status.',
                ),
            );
        }

        if ((await document.status) === 'closed') {
            return [];
        }
        if ((await document.receiptStatus) === 'received') {
            return [];
        }

        // Split lines per stockSite criteria
        const purchaseReceiptData = await xtremPurchasing.functions.PurchaseOrderLib.initPurchaseReceiptCreateData(
            context,
            document,
        );
        return xtremPurchasing.functions.PurchaseOrderLib.createPurchaseReceipt(context, document, purchaseReceiptData);
    }

    /**
     * Method that closes the purchase order
     * @param context
     * @param purchaseOrderNumber
     */
    @decorators.mutation<typeof PurchaseOrder, 'close'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'purchaseOrder',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseOrder,
                isMandatory: true,
            },
        ],
        return: 'boolean',
    })
    static close(context: Context, purchaseOrder: xtremPurchasing.nodes.PurchaseOrder): Promise<boolean> {
        return xtremPurchasing.functions.PurchaseOrderLib.closeOrderLines(context, purchaseOrder);
    }

    /**
     * Method that closes a purchase order line of a purchase order
     * @param context
     * @param purchaseOrderLine contains a unique purchaseOrderLine reference
     * @returns true or throws error
     */
    @decorators.mutation<typeof PurchaseOrder, 'closeLine'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'purchaseOrderLine',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseOrderLine,
                isMandatory: true,
            },
        ],
        return: 'boolean',
    })
    static async closeLine(
        context: Context,
        purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
    ): Promise<boolean> {
        return xtremPurchasing.functions.PurchaseOrderLib.closeOrderLines(context, await purchaseOrderLine.document, [
            purchaseOrderLine._id,
        ]);
    }

    /**
     * Toggle the isSent property to true
     * @param context
     * @param purchaseOrder
     * @returns boolean
     */
    @decorators.mutation<typeof PurchaseOrder, 'setIsSentPurchaseOrder'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseOrder,
            },
        ],
        return: { type: 'boolean' },
    })
    static async setIsSentPurchaseOrder(_context: Context, purchaseOrder: PurchaseOrder): Promise<boolean> {
        await purchaseOrder.$.set({ isSent: true, isPrinted: true });
        await purchaseOrder.$.save();
        return true;
    }

    /**
     * Operation that sends an email with a purchase order pdf attached to an email address.
     * @param context
     * @param purchaseOrder
     * @param contactTitle
     * @param contactLastName
     * @param contactFirstName
     * @param contactEmail
     */
    @decorators.mutation<typeof PurchaseOrder, 'printPurchaseOrderAndEmail'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'purchaseOrder',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseOrder,
            },
            { name: 'contactTitle', type: 'string', isMandatory: true },
            { name: 'contactLastName', type: 'string', isMandatory: true },
            { name: 'contactFirstName', type: 'string', isMandatory: true },
            { name: 'contactEmail', type: 'string', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async printPurchaseOrderAndEmail(
        context: Context,
        purchaseOrder: PurchaseOrder,
        contactTitle: string,
        contactLastName: string,
        contactFirstName: string,
        contactEmail: string,
    ): Promise<boolean> {
        const reports = await xtremMasterData.functions.reportGeneration(context, 'purchaseOrder', '', [
            JSON.stringify({ order: purchaseOrder._id }),
        ]);

        if (reports.length > 0) {
            // prepare the email template data
            const formattedPurchaseOrderDate = (await purchaseOrder.orderDate).format('DD/MM/YYYY');
            const formattedTitle = contactTitle
                ? contactTitle[0].toUpperCase() + contactTitle.substring(1, contactTitle.length).toLowerCase()
                : '';
            const data = {
                purchaseOrder: {
                    number: await purchaseOrder.number,
                    date: formattedPurchaseOrderDate,
                },
                contact: {
                    title: formattedTitle,
                    firstName: contactFirstName,
                    lastName: contactLastName,
                },
                site: {
                    name: await (await purchaseOrder.site).name,
                },
                user: {
                    firstName: await ((await purchaseOrder.$.createdBy) as xtremSystem.nodes.User).firstName,
                    lastName: await ((await purchaseOrder.$.createdBy) as xtremSystem.nodes.User).lastName,
                },
            } as xtremPurchasing.interfaces.PurchaseOrderEmailTemplateData;
            const subject = context.localize(
                '@sage/xtrem-purchasing/purchase_order__email_subject',
                '{{siteName}}: Order {{purchaseOrderNumber}}',
                {
                    siteName: data.site.name,
                    purchaseOrderNumber: data.purchaseOrder.number,
                },
            );

            // email recipients
            const recipients: xtremMailer.interfaces.Recipients = {
                to: [{ address: contactEmail, name: contactEmail }],
            };

            // send the email
            await xtremMasterData.functions.sendReportsByMail(
                context,
                'purchase_order_send',
                data,
                subject,
                recipients,
                `Purchase order - ${await purchaseOrder.number}`,
                reports,
                purchaseOrder,
            );
            return true;
        }
        return false;
    }

    /**
     * Method that creates a purchase order for a given requirement
     * @param context
     * @param site
     * @param item
     * @param supplier
     * @param quantity
     * @param orderDate
     */
    @decorators.mutation<typeof PurchaseOrder, 'createPurchaseOrderReplenishment'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    site: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremSystem.nodes.Site,
                    },
                    item: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremMasterData.nodes.Item,
                    },
                    supplier: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremMasterData.nodes.Supplier,
                    },
                    grossPrice: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    unit: {
                        type: 'reference',
                        isMandatory: true,
                        node: () => xtremMasterData.nodes.UnitOfMeasure,
                    },
                    quantity: {
                        type: 'decimal',
                        isMandatory: true,
                    },
                    priceOrigin: {
                        type: 'enum',
                        isMandatory: false,
                        dataType: () => xtremPurchasing.enums.priceOriginDataType,
                    },
                    orderDate: {
                        type: 'date',
                    },
                    expectedReceiptDate: {
                        type: 'date',
                    },
                    storedDimensions: {
                        isNullable: true,
                        type: 'string',
                    },
                    storedAttributes: {
                        isNullable: true,
                        type: 'string',
                    },
                    stockSite: {
                        type: 'reference',
                        isMandatory: false,
                        node: () => xtremSystem.nodes.Site,
                    },
                    currency: {
                        type: 'reference',
                        isMandatory: false,
                        node: () => xtremMasterData.nodes.Currency,
                    },
                },
                isMandatory: true,
            },
        ],
        return: { type: 'reference', node: () => PurchaseOrder },
    })
    static async createPurchaseOrderReplenishment(
        context: Context,
        data: {
            site: xtremSystem.nodes.Site;
            item: xtremMasterData.nodes.Item;
            supplier: xtremMasterData.nodes.Supplier;
            grossPrice: decimal;
            unit: xtremMasterData.nodes.UnitOfMeasure;
            quantity: decimal;
            priceOrigin?: xtremPurchasing.enums.PriceOrigin;
            orderDate?: date;
            expectedReceiptDate?: date;
            storedDimensions?: object;
            storedAttributes?: xtremMasterData.interfaces.StoredAttributes;
            stockSite?: xtremSystem.nodes.Site;
            currency?: xtremMasterData.nodes.Currency;
        },
    ): Promise<PurchaseOrder | null> {
        const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
            site: data.site._id,
            businessRelation: data.supplier._id,
            currency: data.currency ? data.currency._id : (await (await data.site.legalCompany).currency)?._id,
            orderDate: data.orderDate || date.today(),
            fxRateDate: data.orderDate || date.today(),
            lines: [
                {
                    item: data.item._id,
                    quantity: data.quantity,
                    unit: data.unit._id,
                    grossPrice: data.grossPrice,
                    status: 'draft',
                    expectedReceiptDate: data.expectedReceiptDate || date.today(),
                    priceOrigin: data.priceOrigin || null,
                    storedAttributes: data.storedAttributes,
                    storedDimensions: data.storedDimensions,
                    stockSite: data.stockSite ? data.stockSite._id : undefined,
                },
            ],
            status: 'draft',
        });
        await newPurchaseOrder.$.save();

        return newPurchaseOrder ?? null;
    }

    /**
     *  Gets array of filtered users
     * @param criteria search criteria
     * @returns array of users matching criteria
     */
    @decorators.query<typeof PurchaseOrder, 'getFilteredUsers'>({
        isPublished: true,
        parameters: [
            {
                name: 'criteria',
                type: 'object',
                properties: {
                    userType: {
                        type: 'string',
                        isMandatory: true,
                    },
                    isApiUser: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    isActive: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                    isOperatorUser: {
                        type: 'boolean',
                        isMandatory: false,
                    },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    email: 'string',
                    firstName: 'string',
                    lastName: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static getFilteredUsers(context: Context, criteria: UserSearchCriteria): Promise<FilteredUsers[]> {
        return context
            .query(xtremSystem.nodes.User, {
                filter: {
                    ...criteria,
                    _not: { email: '<EMAIL>' },
                },
            })
            .map(async (user: xtremSystem.nodes.User) => {
                return {
                    _id: String(user._id),
                    email: await user.email,
                    firstName: await user.firstName,
                    lastName: await user.lastName,
                };
            })
            .toArray();
    }

    @decorators.asyncMutation<typeof PurchaseOrder, 'createTestPurchaseOrders'>({
        isPublished: true,
        startsReadOnly: false,
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        parameters: [
            {
                name: 'supplierId',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'itemId',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'itemQuantity',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'orderQuantity',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'numberOfLinesPerOrder',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'orderNumberRoot',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'fixedNumberOfLines',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async createTestPurchaseOrders(
        context: Context,
        supplierId: string,
        itemId: string,
        itemQuantity: integer,
        orderQuantity: integer,
        numberOfLinesPerOrder: integer,
        orderNumberRoot = '',
        fixedNumberOfLines = false,
    ) {
        const item = await context.read(xtremMasterData.nodes.Item, {
            id: itemId,
        });
        const businessRelation = await context.read(xtremMasterData.nodes.Supplier, {
            businessEntity: `#${supplierId}`,
        });

        const site = await (await item.itemSites.at(0))?.site;

        for (let i = 0; i < orderQuantity; i += 1) {
            let lineNumber = 1;
            if (fixedNumberOfLines) {
                lineNumber = numberOfLinesPerOrder;
            } else {
                lineNumber = xtremMasterData.functions.randomInteger(1, numberOfLinesPerOrder);
            }
            let itemNumber = xtremMasterData.functions.randomInteger(1, itemQuantity);
            let lineItem = await context.read(xtremMasterData.nodes.Item, {
                id: `${itemId}-${itemNumber}`,
            });
            let itemDescription = `${await lineItem.description}`;
            const orderNumber = orderNumberRoot || 'MRPTEST';
            const newOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
                number: `${orderNumber}-${i}`,
                site,
                orderDate: date.today(),
                businessRelation,
                lines: [
                    {
                        item: lineItem,
                        itemDescription,
                        quantity: xtremMasterData.functions.randomInteger(1, 20),
                        expectedReceiptDate: date.today().addDays(xtremMasterData.functions.randomInteger(0, 50)),
                    },
                ],
            });
            if (lineNumber > 1) {
                for (let l = 2; l < lineNumber; l += 1) {
                    itemNumber = xtremMasterData.functions.randomInteger(1, itemQuantity);
                    lineItem = await context.read(xtremMasterData.nodes.Item, {
                        id: `${itemId}-${itemNumber}`,
                    });
                    itemDescription = `${await lineItem.description}`;
                    await newOrder.lines.append({
                        item: lineItem,
                        itemDescription,
                        quantity: xtremMasterData.functions.randomInteger(1, 20),
                        expectedReceiptDate: date.today().addDays(xtremMasterData.functions.randomInteger(0, 50)),
                    });
                }
            }

            await newOrder.$.save();
        }
        return `Number of items created - ${orderQuantity}`;
    }

     @decorators.mutation<typeof PurchaseOrder, 'beforePrintPurchaseOrder'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'order',
                type: 'reference',
                isMandatory: true,
                node: () => xtremPurchasing.nodes.PurchaseOrder,
            },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintPurchaseOrder(context: Context, order: PurchaseOrder): Promise<boolean> {
        if ((await order.taxCalculationStatus) === 'failed') {
            await context.batch.logMessage(
                'error',
                `Cannot print the document. Tax calculation failed. Document details: ${JSON.stringify({
                    number: await order.number,
                    status: await order.status,
                    taxCalculationStatus: await order.taxCalculationStatus,
                    displayStatus: await order.displayStatus,
                })}`,
            );

            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_order__print_tax_calculation_failed',
                    'The tax calculation for this order failed. You can print this order after the tax details are corrected.',
                ),
            );
        }

        if ((await order.status) === 'error') {
            await context.batch.logMessage(
                'error',
                `Cannot print the document. Document details: ${JSON.stringify({
                    number: await order.number,
                    status: await order.status,
                    taxCalculationStatus: await order.taxCalculationStatus,
                    displayStatus: await order.displayStatus,
                })}`,
            );

            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_order__print_error',
                    'You need to correct the order details before you can print the order.',
                ),
            );
        }

        await context.batch.logMessage(
            'info',
            `Purchase order ${await order.number} validation for printing completed successfully.`,
        );

        return true;
    }

     @decorators.bulkMutation<typeof PurchaseOrder, 'printBulk'>({
        isPublished: true,
        startsReadOnly: true,
        queue: 'reporting',
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-purchasing/node__purchase_order_bulk_print_report_name',
                'Purchase order',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'purchaseOrder',
                documents: reports,
                reportName,
            });
        },
    })
    static printBulk(context: Context, document: PurchaseOrder) {
        return xtremReporting.nodes.Report.generateReportPdf(context, 'purchaseOrder', '', {
            variables: JSON.stringify({
                order: document._id,
            }),
            isBulk: true,
        });
    }

    @decorators.mutation<typeof PurchaseOrder, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseOrder,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        let financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
            wasSuccessful: false,
            message: '',
        };

        const purchaseReceiptData = await xtremPurchasing.functions.PurchaseOrderLib.initPurchaseReceiptCreateData(
            context,
            purchaseOrder,
            { isForFinanceCheck: true },
        );

        await asyncArray(purchaseReceiptData).forEach(async purchaseReceiptDataRecord => {
            const purchaseReceipt = await context.create(
                xtremPurchasing.nodes.PurchaseReceipt,
                {
                    ...purchaseReceiptDataRecord,
                },
                { isTransient: true },
            );
            if (await purchaseReceipt.$.control()) {
                financeIntegrationCheckResult =
                    await xtremPurchasing.functions.FinanceIntegration.purchaseReceiptControlAndCreateMutationResult(
                        context,
                        purchaseReceipt,
                    );
            } else {
                financeIntegrationCheckResult.message = JSON.stringify(purchaseReceipt.$.context.diagnoses);
            }
        });
        return financeIntegrationCheckResult;
    }

    // Repost first updates the purchase order lines with the new attributes and dimensions.
    // If we have a finance transaction reference, there might be data already on the acc staging table,
    // meaning that we also need to send an update notification to update the acc staging,
    // if there records are not yet successfully processed (for that we check the status of the finance transaction).
    // If there's no finance transaction, we only update the PO lines.
    @decorators.mutation<typeof PurchaseOrder, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseOrder,
            },
            {
                name: 'orderLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
            { name: 'saveOnly', type: 'boolean' },
            {
                name: 'financeTransaction',
                type: 'reference',
                isWritable: true,
                node: () => xtremFinanceData.nodes.FinanceTransaction,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async repost(
        context: Context,
        purchaseOrder: PurchaseOrder,
        orderLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
        saveOnly: boolean,
        financeTransaction?: xtremFinanceData.nodes.FinanceTransaction,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        // If there's no finance transaction (but no actual repost also, just a att and dim update on PO without updating nothing on acc staging)  If we have a finance transaction (we want to update the invoice acc staging records, not the receipt ones)
        if (financeTransaction) {
            if (
                !xtremFinanceData.functions.canRepost((await financeTransaction?.status) || 'submitted') // submitted used in case of undefined to assure that function will return false
            ) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-purchasing/nodes__purchase-order__cant_repost_purchase_order_when_status_is_not_failed',
                        "You can only repost a purchase order if the status is 'Failed' or 'Not recorded.'",
                    ),
                );
            }
        }

        const landedCostServiceOption = await context.isServiceOptionEnabled(
            xtremMasterData.serviceOptions.landedCostOption,
        );

        const updateLines = orderLines.map(orderLine => {
            return {
                _id: orderLine.baseDocumentLineSysId,
                storedAttributes: orderLine.storedAttributes,
                storedDimensions: orderLine.storedDimensions,
            };
        });

        await purchaseOrder.$.set({
            forceUpdateForFinance: true,
            lines: updateLines,
        });
        await purchaseOrder.$.save();

        if (financeTransaction && landedCostServiceOption) {
            // send notification in order to update a staging table entry for the accounting engine
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: purchaseOrder, // will not be used since we send the finance transaction
                lines: await xtremPurchasing.nodes.PurchaseOrder.getRelatedInvoiceLines(purchaseOrder.lines),
                documentType: await financeTransaction.documentType,
                // TODO: on purchase receipt, check if this is correct, should it be PurchaseInvoice in some cases?
                replyTopic: 'PurchaseOrder/accountingInterface',
                doNotPostOnUpdate: saveOnly,
                financeTransactionRecord: financeTransaction,
                sourceDocumentNumber: await purchaseOrder.number,
            });
        }

        if (saveOnly) {
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase-order__document_was_saved',
                    'The purchase order was saved.',
                ),
            };
        }

        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-purchasing/nodes__purchase-order__document_was_posted',
                'The purchase order was posted.',
            ),
        };
    }

    override async resynchronize(): Promise<boolean> {
        await super.resynchronize();
        return xtremPurchasing.functions.PurchaseOrderLib.resynchronizeStatus(this.$.context, this);
    }

    static async getRelatedInvoiceLines(
        orderLines: Collection<xtremPurchasing.nodes.PurchaseOrderLine>,
    ): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLineNodeUpdate[]> {
        return (
            await orderLines
                .map(orderLine => {
                    return orderLine.landedCostLines
                        .map(async landedCostLine => {
                            return {
                                _id: (await (await (await landedCostLine.landedCostAllocation).line).documentLine)._id,
                                sourceBaseDocumentLineSysId: orderLine._id,
                                storedAttributes: (await orderLine.storedAttributes) || {},
                                storedDimensions: (await orderLine.storedDimensions) || {},
                                computedAttributes: await orderLine.computedAttributes,
                            };
                        })
                        .toArray();
                })
                .toArray()
        ).flat(1);
    }

    override updateDocumentLineTo(
        lines:
            | Collection<xtremPurchasing.nodes.PurchaseOrderLine>
            | AsyncArrayReader<xtremPurchasing.nodes.PurchaseOrderLine>,
    ): Promise<boolean> {
        return xtremPurchasing.functions.PurchaseInvoiceLib.updateDocumentLineStatuses(this, lines);
    }


}
