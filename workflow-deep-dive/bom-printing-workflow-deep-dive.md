# BOM Printing: A Deep Dive

This document provides a detailed, step-by-step breakdown of the Bill of Material (BOM) printing workflow. It is intended for developers, testers, and business analysts who need a thorough understanding of the entire process, from the initial user action to the final state changes in the system.

---

## Part 1: The Big Picture - Visual Workflow

This flowchart illustrates the complete, end-to-end process of printing a Bill of Material.

```mermaid
graph TD
    subgraph User Action
        A[User clicks "Print" on a Bill of Material]
    end

    subgraph Print Engine
        A --> B[Mutation 'generateBomReport' on BillOfMaterialPrintout node is called];
        B --> C[createBomPrintout is called, creating a temporary BillOfMaterialPrintout record];
        C --> D[generateReportPdf is called with the bomPrintoutId];
        D --> E[PDF is generated];
        E --> F[Temporary BillOfMaterialPrintout record is deleted];
        F --> G[Final PDF returned to user];
    end
```

---

## Part 2: The Workflow Step-by-Step

### Step 1: Initiation - `generateBomReport` Async Mutation

The process begins when a user triggers the `generateBomReport` async mutation on the `BillOfMaterialPrintout` node. This is the primary entry point for the entire workflow. It takes several parameters, including the `bomId` and options like `singleLevel`, `omitCost`, and `omitInstruction`.

### Step 2: Data Preparation - `createBomPrintout`

The `generateBomReport` mutation first calls the `createBomPrintout` static method. This method is responsible for creating a temporary `BillOfMaterialPrintout` record that will be used as the data source for the report.

*   **Purpose**: To create a snapshot of the BOM data for the report.
*   **Core Logic**:
    1.  **Create `BillOfMaterialPrintout`**: It creates a new `BillOfMaterialPrintout` record.
    2.  **Populate Components**: It calls the `bomMultiLevelComponents` method to recursively gather all the components of the BOM and populates the `components` collection on the new `BillOfMaterialPrintout` record.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts

static async createBomPrintout(
    context: Context,
    bom: xtremTechnicalData.nodes.BillOfMaterial,
    singleLevel = false,
    baseQuantity?: decimal,
): Promise<xtremTechnicalData.nodes.BillOfMaterialPrintout> {
    const bomPrintout = await context.create(xtremTechnicalData.nodes.BillOfMaterialPrintout, {
        id: context.batch.trackingId,
        bomCode: bom,
        components: await BillOfMaterialPrintout.bomMultiLevelComponents(
            context,
            bom,
            baseQuantity || (await bom.baseQuantity),
            singleLevel,
        ),
    });

    await bomPrintout.$.save();

    return bomPrintout;
}
```

### Step 3: PDF Generation

Once the `BillOfMaterialPrintout` record is created, the `generateBomReport` mutation calls the `generateReportPdf` function from the `xtrem-reporting` package. It passes the `bomPrintoutId` and the other report parameters as variables.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts

report = await xtremReporting.nodes.Report.generateReportPdf(context, 'bomMultiLevel', '', {
    variables: JSON.stringify({
        bomPrintoutId: await bomPrintout.id,
        singleLevel: singleLevel ? 'true' : 'false',
        omitCost: omitCost ? 'true' : 'false',
        omitInstruction: omitInstruction ? 'true' : 'false',
    }),
});
```

### Step 4: Cleanup

After the PDF has been generated, the `generateBomReport` mutation deletes the temporary `BillOfMaterialPrintout` record to avoid cluttering the database.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts

if (!skipDelete) {
    await bomPrintout.$.delete();
    await context.batch.logMessage('info', 'BOM printout data deleted');
}
```

---

## Part 3: Key Takeaways

*   **No Pre/Post Operations**: The `bomMultiLevel` report does not use the standard pre- and post-processing operations. Instead, all the logic is contained within the `generateBomReport` async mutation.
*   **Temporary Data**: The workflow creates a temporary `BillOfMaterialPrintout` record to serve as the data source for the report, which is then deleted after the report is generated.
*   **Self-Contained**: The entire process, from data preparation to cleanup, is handled within the `generateBomReport` mutation, making it a self-contained and robust workflow.
