# Sales Invoice Printing: A Deep Dive

This document provides a detailed, step-by-step breakdown of the sales invoice printing workflow. It is intended for developers, testers, and business analysts who need a thorough understanding of the entire process, from the initial user action to the final state changes in the system.

---

## Part 1: The Big Picture - Visual Workflow

This flowchart illustrates the complete, end-to-end process of printing a sales invoice.

```mermaid
graph TD
    subgraph User Action
        A[User clicks "Print" on a Sales Invoice]
    end

    subgraph Print Engine & Pre-Processing
        A --> B[Mutation 'print' on SalesInvoice node is called];
        B --> C{Print Engine reads report.csv};
        C --> D[Finds 'salesInvoice' entry];
        D --> E[Identifies pre-processing op: 'beforePrint'];
        E --> F[Engine executes 'beforePrint'];
        F --> G[Operation validates Invoice status and data integrity];
        G --> H{Validation OK?};
    end

    subgraph PDF Generation
        H -- Yes --> I[Generate PDF document];
    end

    subgraph Post-Processing & Final State
        I --> J[Engine identifies post-processing op: 'afterPrint'];
        J --> K[Engine executes 'afterPrint'];
        K --> L[Operation sets 'isPrinted' flag to true];
        L --> M[Invoice status is updated from 'draft' to 'posted'];
        M --> N[Final PDF returned to user];
    end

    subgraph Error Path
        H -- No --> X[Abort process, return error to user];
    end
```

---

## Part 2: Configuration - The CSV Drivers

The entire printing process is orchestrated by two critical CSV files that define the report's behavior and the data it needs.

### 1. `report.csv` - The Workflow Definition

This file acts as the master configuration for the report, linking the report name to its corresponding logic.

**Entry for Sales Invoice:**
`"salesInvoice";"sage";"Sales invoice";"xtrem-sales";"salesInvoice";"printedDocument";"Y";"single";"SalesInvoice|beforePrint|";"SalesInvoice|afterPrint|"`

This line tells the system:
*   **Report Name**: `salesInvoice`
*   **Pre-Processing Operation**: `beforePrint` (found on the `SalesInvoice` node)
*   **Post-Processing Operation**: `afterPrint` (found on the `SalesInvoice` node)

### 2. `report-variable.csv` - The Data Pipeline

This file defines the dynamic data that will be injected into the printing process. For the sales invoice, it specifies that the `invoice` ID is a required parameter.

| Report Name | Variable Name | Type | Mandatory | Description |
| :--- | :--- | :--- | :--- | :--- |
| `salesInvoice` | `invoice` | `reference` | `true` | The ID of the Sales Invoice to print. |

When a user prints an invoice, the system captures the ID of that specific record and makes it available to the pre- and post-processing operations.

---

## Part 3: The Workflow Step-by-Step

### Step 1: Initiation - `print` Mutation

The process begins when a user triggers the `print` mutation on the `SalesInvoice` node. This is the primary entry point for the entire workflow.

### Step 2: Pre-Processing - `beforePrint`

Before any PDF is generated, the print engine executes the `beforePrint` operation. This function acts as a critical validation gatekeeper.

*   **Purpose**: To ensure the invoice is in a valid state for printing.
*   **Core Logic**:
    1.  **Status Validation**: It checks the invoice `status`. Printing is allowed for `draft` and `posted` invoices but is blocked for `error` status. For `inProgress` status, it ensures all financial transactions are fully recorded.
    2.  **Tax Calculation Status**: It verifies the `taxCalculationStatus`, blocking the print if it has `failed` to prevent sending financially incorrect documents.
    3.  **Document Integrity**: It ensures the invoice has at least one line item.

If any of these checks fail, the operation will throw a `BusinessRuleError`, which stops the printing process immediately and displays an error message to the user.

### Step 3: PDF Generation

If the `beforePrint` operation completes successfully, the print engine proceeds to generate the physical PDF document using the active `salesInvoice` template.

### Step 4: Post-Processing - `afterPrint`

After the PDF has been successfully created, the engine calls the `afterPrint` operation. This function is responsible for performing critical side effects and updating the state of the invoice.

*   **Purpose**: To record that the invoice has been printed and to trigger any downstream financial or status updates.
*   **Core Logic**:
    1.  **Set `isPrinted` Flag**: The most important action is setting the `isPrinted` boolean property on the `SalesInvoice` instance to `true`.
    2.  **Update Status to `posted`**: If the invoice was in `draft` status, it is updated to `posted`. This is a key finalization step, preventing further edits.
    3.  **Financial Integration**: It notifies the financial system that the invoice has been issued, which can trigger subsequent workflows like payment tracking.

```typescript
// Simplified conceptual logic for afterPrint
async function afterPrint(context, invoiceId: string): Promise<void> {
    const invoice = await context.read(SalesInvoice, { _id: invoiceId }, { forUpdate: true });

    // 1. Set the printed flag
    await invoice.$.set({ isPrinted: true });

    // 2. Update the status if it was a draft
    if (await invoice.status === 'draft') {
        await invoice.$.set({ status: 'posted' });
    }

    // 3. Notify financial systems (conceptual)
    await context.callService('finance.notifyInvoicePrinted', { invoiceId: invoice._id });

    await invoice.$.save();
}
```

---

## Part 4: Key Takeaways

*   **Configuration is King**: The entire workflow is driven by the `report.csv` and `report-variable.csv` files. To understand a report's behavior, always start here.
*   **Pre-Processing is for Validation**: The `beforePrint` operation is a crucial guardrail that enforces business rules and prevents the printing of invalid or inconsistent invoices.
*   **Post-Processing is for State Changes**: The `afterPrint` operation finalizes the document by updating its state (`isPrinted`, `status`) and notifying other parts of the system.
*   **The Flow is Synchronous**: From the user's perspective, the entire process is a single, synchronous operation. The print engine waits for each step to complete before moving to the next.
