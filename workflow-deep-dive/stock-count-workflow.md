# Stock Count Workflow

This document outlines the process and validations involved in performing a stock count within the system. The workflow is divided into several key stages, from creation to posting.

## 1. Statuses

The stock count process is managed through the following statuses:

-   `draft`: The initial status of a stock count.
-   `toBeCounted`: The stock count is ready to be started.
-   `countInProgress`: The stock count is actively being worked on.
-   `counted`: All lines on the stock count have been counted.
-   `closed`: The stock count has been posted, and stock levels have been adjusted.

## 2. Creation and Preparation

1.  A stock count is created with a `draft` status.
2.  Criteria such as the site, item ranges, locations, and zones are defined.
3.  The `confirmStockCount` action is used to generate the lines of the stock count based on the selected criteria. The status then changes to `toBeCounted`.

## 3. Counting Process

1.  The `start` action initiates the counting process, changing the status to `countInProgress`.
2.  Users can then enter the counted quantities for each line.
3.  A specific action, `confirmZeroQuantity`, is available to handle lines where the counted quantity is zero.
4.  Once all lines have been counted, the status automatically updates to `counted`.

## 4. Validations

The system performs several validations throughout the process:

-   **Uniqueness:** The system checks that a line is not already present in another active stock count.
-   **Deletion:** Deletion of stock counts and their lines is restricted based on the status to prevent data loss.
-   **Posting:** Before posting, the system validates:
    -   The status of the count.
    -   That counted quantities are not less than any allocated quantities.

## 5. Posting and Finalization

1.  The `postToStock` action triggers the finalization of the stock count.
2.  The system performs the pre-posting validations.
3.  If the validations pass, the stock adjustments are posted, and the stock levels are updated in the system.
4.  The stock count status is set to `closed`.
5.  If financial integration is enabled, a notification is sent to the finance system.
