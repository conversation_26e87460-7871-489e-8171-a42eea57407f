# Sales Invoice Printing Workflow

This document outlines the process and validations involved in printing a sales invoice within the system. The workflow is divided into three main stages: Pre-Print Validations, Print Execution, and Post-Print Actions.

## 1. Pre-Print Validations

Before a sales invoice can be printed, the system performs a series of checks to ensure the document is in a valid state for printing. These validations are primarily handled by the `checkIfCanPrintSalesInvoice` function.

### Status Validation

The system checks the `status` of the invoice:

-   **Allowed Statuses:** Invoices with a status of `draft` or `posted` can be printed.
-   **Error Status:** Invoices with a status of `error` cannot be printed. The errors must be resolved first.
-   **In Progress Status:** For invoices with a status of `inProgress`, the system checks the associated `financeTransactions`. The invoice can only be printed if all finance transactions are in a `recording` state.

### Tax Calculation Validation

The system checks the `taxCalculationStatus`:

-   If the tax calculation has `failed`, the invoice is blocked from printing.

### Document Integrity Checks

-   The system verifies that the invoice has at least one line item. This is enforced by a `controlEnd` check on the `SalesInvoice` node.

## 2. Print Execution

Once the pre-print validations have passed, the system proceeds with the print execution.

### Standard Print Process

1.  The printing process is initiated through the `beforePrint` mutation.
2.  The system displays a print dialog to the user.
3.  A PDF of the invoice is generated using a report template from the reporting module (`xtremReporting`).

### Bulk Print Implementation

-   The system supports printing multiple invoices at once via the `printBulkSalesInvoice` mutation.
-   Each invoice in the bulk request is validated individually using the same pre-print validation logic.
-   Reports are generated for all invoices that pass the validation checks.

## 3. Post-Print Actions

After the invoice has been successfully printed, the system performs several actions to update the document's state and notify other parts of the system.

### Flag Updates

-   The `isPrinted` flag on the invoice is set to `true`.
-   If the invoice is printed and emailed in a single operation (using the `printSalesInvoiceAndEmail` mutation), the `isSent` flag is also set to `true`.

### Financial System Integration

-   **Intacct Integration:** For customers using the Intacct finance integration, the system updates payment-related amounts on the invoice.
-   **Finance Notification:** The system sends a notification to the finance module to inform it that the document has been printed. This is handled by the `notifyDocumentIsPrinted` function.
