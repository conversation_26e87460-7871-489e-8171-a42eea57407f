# Stock Count Printing Workflow: A Deep Dive

This document provides a detailed analysis of the printing workflow specifically for Stock Counts. Unlike Sales Invoices, the Stock Count printing process is more straightforward, relying heavily on a generic reporting module without extensive document-specific pre- or post-print validations.

**Key Files Involved:**
-   `services/applications/xtrem-stock/lib/pages/stock-count.ts` (Defines the UI actions for printing)
-   `services/applications/xtrem-stock/lib/nodes/stock-count.ts` (The core data model, which does not contain direct printing logic)
-   `services/applications/xtrem-stock/lib/functions/stock-count-lib.ts` (Contains shared validation and business logic)

---

## Part 1: Entry Point - Initiating a Print

### 1.1 The 'Print' Page Action

-   **Location:** `pages/stock-count.ts` (`@ui.decorators.pageAction<StockCount>({ title: 'Print', ... })`)
-   **Trigger:** A user clicks the 'Print' button on the Stock Count detail page.
-   **Action:** When clicked, this action sets `this.printSelectBlock.isHidden = false;`, making a hidden UI block visible. It then opens a custom dialog (`this.$.dialog.custom(...)`) to display this `printSelectBlock` to the user.
-   **End Value:** The user is presented with a small dialog containing print options.

```typescript
    @ui.decorators.pageAction<StockCount>({
        title: 'Print',
        icon: 'print',
        async onClick() {
            this.printSelectBlock.isHidden = false;
            await this.$.dialog.custom('info', this.printSelectBlock, {
                resolveOnCancel: true,
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
            });

            this.$.setPageClean();
        },
    })
    print: ui.PageAction;
```

---

## Part 2: Print Options

### 2.1 'Show quantity in stock' Switch

-   **Location:** `pages/stock-count.ts` (`@ui.decorators.switchField<StockCount>({ title: 'Show quantity in stock', ... })` within `printSelectBlock`)
-   **Purpose:** This UI control allows the user to specify whether the printed report should include the 'quantity in stock' alongside the 'counted quantity'. This is a user preference for the report content.
-   **End Value:** A boolean value (`true` or `false`) is captured, which will be passed to the reporting module.

```typescript
    @ui.decorators.switchField<StockCount>({
        title: 'Show quantity in stock',
        isTransient: true,
        parent() {
            return this.printSelectBlock;
        },
    })
    showQuantityInStock: ui.fields.Switch;
```

---

## Part 3: Print Execution

### 3.1 The 'Print' Button (within the dialog)

-   **Location:** `pages/stock-count.ts` (`@ui.decorators.buttonField<StockCount>({ map() { return ui.localize('...', 'Print'); }, ... })` within `printSelectBlock`)
-   **Trigger:** The user clicks the 'Print' button within the print options dialog.
-   **Action:** The `onClick` handler for this button performs the following:
    1.  Hides the `printSelectBlock` dialog (`this.printSelectBlock.isHidden = true;`).
    2.  Invokes the generic reporting module by calling `this.$.dialog.page('@sage/xtrem-reporting/PrintDocument', {...})`.
-   **Parameters Passed to Reporting Module:**
    -   `reportName: 'stockCount'`: Identifies the specific report template to be used for stock counts.
    -   `stockCount: this.$.recordId ?? ''`: Passes the unique identifier of the current stock count document to the report.
    -   `showQuantityInStock: this.showQuantityInStock.value ?? true`: Passes the user's preference for including stock quantities in the report.
-   **End Value:** The system initiates the generation and display of the Stock Count report, typically in a new window or tab, handled by the `PrintDocument` page of the reporting module.

```typescript
    @ui.decorators.buttonField<StockCount>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.printSelectBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock/stock_count_pages__stock_count_print', 'Print');
        },
        async onClick() {
            this.printSelectBlock.isHidden = true;
            this.printSelectBlock.isHidden = true;
            await this.$.dialog.page(
                '@sage/xtrem-reporting/PrintDocument',
                {
                    reportName: 'stockCount',
                    stockCount: this.$.recordId ?? '',
                    showQuantityInStock: this.showQuantityInStock.value ?? true,
                },
                { size: 'extra-large' },
            );
        },
        isTitleHidden: true,
    })
    printDocument: ui.fields.Button;
```

---

## Part 4: Document Integrity Validations (Ensuring a Valid Document for Printing)

While the printing process itself for Stock Counts does not involve direct pre-print validations, the underlying Stock Count document undergoes various validations throughout its lifecycle. These validations ensure the integrity and accuracy of the document, making it a reliable source for printing.

### 4.1 Duplicate Lines Check (`controlEnd`)

-   **Description:** This validation prevents the addition of duplicate lines within a stock count. It checks for identical combinations of item, stock status, owner, location, and lot/sublot.
-   **Purpose/End Value:** Ensures that each item/location/lot combination is counted only once, preventing data redundancy and errors in the final stock adjustment. This is crucial for accurate inventory records.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts`

```typescript
    async controlEnd(cx) {
        // check for duplicate lines
        const addedLines = this.lines.filter(line => line.$.status === NodeStatus.added);
        await addedLines.forEach(async (line1, index1) => {
            await addedLines.forEach(async (line2, index2) => {
                if (index2 <= index1 || (await line1.item)._id !== (await line2.item)._id) {
                    return;
                }
                const stockDetailLot1 = await (await line1.stockDetails.elementAt(0)).stockDetailLot;
                const stockDetailLot2 = await (await line2.stockDetails.elementAt(0)).stockDetailLot;
                const hasDuplicatesIndependentFromLotReference =
                    (await line1.stockStatus)?._id === (await line2.stockStatus)?._id &&
                    (await line1.owner) === (await line2.owner) &&
                    (await line1.location)?._id === (await line2.location)?._id &&
                    (await stockDetailLot1?.sublot) === (await stockDetailLot2?.sublot);
                const hasDuplicatesAndLotReference =
                    (await stockDetailLot1?.lot) &&
                    (await stockDetailLot1?.lot)?._id === (await stockDetailLot2?.lot)?._id;
                const hasDuplicatesAndNoLotReference =
                    !(await stockDetailLot1?.lot) &&
                    (await stockDetailLot1?.lotNumber) &&
                    (await stockDetailLot1?.lotNumber) === (await stockDetailLot2?.lotNumber);
                if (
                    hasDuplicatesIndependentFromLotReference &&
                    (hasDuplicatesAndLotReference || hasDuplicatesAndNoLotReference)
                ) {
                    cx.at('lines', `${line1._id}`).error.addLocalized(
                        '@sage/xtrem-stock/pages__stock_count__duplication_among_added_lines',
                        'Change or remove duplicate lines.',
                    );
                }
            });
        });
    },
```

### 4.2 Deletion Control (`controlDelete`)

-   **Description:** This validation restricts the deletion of a stock count based on its current `status` and whether it contains any lines.
-   **Purpose/End Value:** Prevents accidental or unauthorized deletion of active or partially completed stock counts, ensuring data integrity and maintaining an audit trail of inventory activities.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts`

```typescript
    async controlDelete(cx) {
        if ((await this.status) !== 'toBeCounted' && (await this.lines.length) > 0) {
            cx.error.addLocalized(
                '@sage/xtrem-stock/nodes__stock_count__deletion_forbidden',
                'You cannot delete the stock count.',
            );
        }
    },
```

### 4.3 Last Count Date vs. Effective Date (`lastCountDate` property control)

-   **Description:** This control ensures that the `lastCountDate` (a criterion for selecting items not counted as of a certain date) is not set to a date after the `effectiveDate` of the stock count itself.
-   **Purpose/End Value:** Maintains chronological consistency within the document. An effective date represents when the count is valid, and a last count date should logically precede or be on the effective date.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts` (within the `lastCountDate` property decorator)

```typescript
    @decorators.dateProperty<StockCount, 'lastCountDate'>({ // ... other decorators
        async control(cx, val) {
            if (val) {
                await cx.error.if(val).is.after(await this.effectiveDate);
            }
        },
    })
    readonly lastCountDate: Promise<date | null>;
```

### 4.4 Reposting Status Check (`repost` mutation)

-   **Description:** This validation ensures that a stock count can only be reposted to the finance system if its `financeIntegrationStatus` is either `'Failed'` or `'Not recorded'`.
-   **Purpose/End Value:** Prevents unnecessary or erroneous reposting of stock counts that have already been successfully integrated with the finance system, ensuring data consistency between inventory and financial records.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts`

```typescript
    static async repost(
        context: Context,
        stockCount: StockCount,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost(await stockCount.financeIntegrationStatus)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__cant_repost_stock_count_when_status_is_not_failed',
                    "You can only repost a stock count if the status is 'Failed' or 'Not recorded'.",
                ),
            );
        }
        // ... rest of repost logic
    }
```

### 4.5 Start Action Status Check (`start` mutation)

-   **Description:** This validation ensures that the `start` action (which moves the stock count to `countInProgress` status) is only performed if the stock count is currently in a `toBeCounted` status.
-   **Purpose/End Value:** Maintains the integrity of the stock count workflow, preventing a count from being started if it's already in progress, closed, or in an invalid state. This ensures a clear progression through the counting stages.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts` (The UI also controls this via `setStartAction` in `pages/stock-count.ts`)

```typescript
    setStartAction(isDirty: boolean) {
        this.start.isDisabled = isDirty || this.status.value !== 'toBeCounted';
        this.start.isHidden = this.start.isDisabled;
    }
```

### 4.6 Post to Stock Pre-Posting Controls (`controlBeforePosting`)

-   **Description:** This is a set of critical validations performed just before the stock count is posted to the stock service. It aggregates checks from several sub-functions.
-   **Purpose/End Value:** Ensures that the stock count is in a valid and complete state for finalization, preventing incorrect or incomplete stock adjustments from being applied to the inventory.
-   **Location:** `services/applications/xtrem-stock/lib/functions/stock-count-lib.ts` (called by the `postToStock` mutation in `nodes/stock-count.ts`)

```typescript
    async controlBeforePosting(context: Context, documentIds: integer[]) {
        await this.controlStatusBeforePosting(context, documentIds);
        await this.controlAllocationsBeforePosting(context, documentIds);
        await this.controlStockRecordBeforePosting(context, documentIds);
    }

    // Sub-validations within controlBeforePosting:
    private async controlStatusBeforePosting(context: Context, documentIds: integer[]) {
        const wrongDocument = await context
            .query(xtremStock.nodes.StockCount, { filter: { _id: { _in: documentIds }, status: { _ne: 'counted' } }, first: 1 })
            .at(0);
        if (wrongDocument) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__counting_is_not_finished_yet',
                    'You cannot post a stock count when the count is not finished: stock count {{number}}.',
                    { number: await wrongDocument.number },
                ),
            );
        }
    }

    private async controlAllocationsBeforePosting(context: Context, documentIds: integer[]) {
        const wrongDocumentLine = (
            await context.select(
                xtremStock.nodes.StockCountLine,
                { documentNumber: true, item: { id: true }, countedQuantityInStockUnit: true, totalAllocated: true },
                { filter: { documentId: { _in: documentIds }, hasAllocationError: true }, first: 1 },
            )
        ).at(0);
        if (wrongDocumentLine) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__counted_quantity_less_than_allocated_quantity',
                    'For stock count {{number}}, the counted quantity {{countedQuantity}} for {{item}} cannot be less than the allocated quantity {{totalAllocated}}.',
                    {
                        number: wrongDocumentLine.documentNumber,
                        item: wrongDocumentLine.item.id,
                        countedQuantity: wrongDocumentLine.countedQuantityInStockUnit,
                        totalAllocated: wrongDocumentLine.totalAllocated,
                    },
                ),
            );
        }
    }

    private async controlStockRecordBeforePosting(context: Context, documentIds: integer[]) {
        const wrongItem =
            (
                await context.select(
                    xtremStock.nodes.StockCountLine,
                    { item: { id: true } },
                    { filter: { documentId: { _in: documentIds }, status: 'counted', stockRecord: null, isCreatedFromStockRecord: true }, first: 1 },
                )
            ).at(0)?.item.id ?? '';
        if (wrongItem) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__counted_stock_record_not_found',
                    'The stock count record for item {{item}} has changed since the stock count started. Please exclude this line from the stock count.',
                    {
                        item: wrongItem,
                    },
                ),
            );
        }
    }
```

### 4.7 Confirm Zero Quantity Status Check (`confirmZeroQuantity` mutation)

-   **Description:** This validation ensures that the `confirmZeroQuantity` action is only performed when the stock count is in an appropriate state for this operation.
-   **Purpose/End Value:** Prevents confirming zero quantities on counts that haven't been started yet (`toBeCounted`) or have already been finalized (`closed`), maintaining the integrity of the counting process.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts`

```typescript
    static async confirmZeroQuantity(context: Context, number: string): Promise<integer> {
        // ... (logging and initial setup)
        const stockCount = await context.read(StockCount, { number }, { forUpdate: true });

        if ((await stockCount.status) === 'toBeCounted') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__to_be_counted_confirm_zero_cannot_be_performed',
                    'The stock count has not started yet: {{number}}. Start the count to update the quantities.',
                    { number },
                ),
            );
        }
        if ((await stockCount.status) === 'closed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__closed_confirm_zero_cannot_be_performed',
                    'You cannot update the quantities on a stock count that was closed: stock count {{number}}.',
                    { number },
                ),
            );
        }
        // ... rest of confirmZeroQuantity logic
    }
```

### 4.8 Confirm Stock Count - No Records Selected (`confirmStockCount` mutation)

-   **Description:** This validation ensures that when a user attempts to confirm the stock count (i.e., generate its lines), at least one record (either a stock record or an item site) has been selected.
-   **Purpose/End Value:** Prevents the creation of an empty or meaningless stock count, ensuring that the document always represents a valid counting activity.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts`

```typescript
    static async confirmStockCount(
        context: Context,
        stockCount: xtremStock.nodes.StockCount,
        selectedRecords: { allSelected: boolean; stockRecords: Array<number>; itemSites: Array<number> },
    ): Promise<boolean> {
        // ... (initial setup)
        if (
            !selectedRecords.allSelected &&
            selectedRecords.stockRecords.length === 0 &&
            selectedRecords.itemSites.length === 0
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-stock/nodes__stock_count__no_records_selected',
                    'No records selected for stock count {{number}}.',
                    { number: await stockCount.number },
                ),
            );
        }
        // ... rest of confirmStockCount logic
    }
```

### 4.9 Existing Stock Count Line (`existingStockCountLine` query/function)

-   **Description:** This crucial validation checks for the uniqueness of a stock count line (defined by item, stock status, location, lot, etc.) across all non-closed stock counts in the system.
-   **Purpose/End Value:** Prevents duplicate entries and ensures that an item is not being counted simultaneously in multiple active stock counts. This is vital for maintaining accurate and consistent inventory data.
-   **Location:** `services/applications/xtrem-stock/lib/nodes/stock-count.ts` (as a query) and `services/applications/xtrem-stock/lib/functions/stock-count-lib.ts` (where the core logic resides)

```typescript
// From services/applications/xtrem-stock/lib/functions/stock-count-lib.ts
async existingStockCountLine(
    context: Context,
    throwErrorIfExist: boolean,
    searchCriteria: {
        item: integer;
        stockStatus: integer | null;
        location?: integer;
        lot?: integer;
        stockUnit: integer;
        owner: string;
        document: { stockSite: integer };
    },
    lotCreateData?: {
        id: string;
        sublot: string | null;
    },
): Promise<boolean> {
    const filter: typeof searchCriteria & {
        document: { status?: { _in: Array<xtremStock.enums.StockCountStatus> } };
    } = searchCriteria;
    filter.document.status = { _in: ['toBeCounted', 'counted', 'countInProgress'] };
    const { lot } = searchCriteria;
    if (lot) {
        delete filter.lot;
    }
    let searchResult = context.query(xtremStock.nodes.StockCountLine, {
        filter,
    });
    let result = (await searchResult.length) > 0;
    if (result && lot) {
        searchResult = searchResult.filter(
            async stockCountLine =>
                (await (await (await stockCountLine.stockDetails.at(0))?.stockDetailLot)?.lot)?._id === lot,
        );
    }
    result = (await searchResult.length) > 0;
    let stockCountNumber = result ? await (await searchResult.at(0))?.documentNumber : '';

    if (result && lotCreateData) {
        result = await searchResult.some(async stockCountLine => {
            const stockDetailLot = await (await stockCountLine.stockDetails.at(0))?.stockDetailLot;
            if (stockDetailLot == null) {
                return false;
            }
            stockCountNumber = await stockCountLine.documentNumber;
            return (
                (await stockDetailLot.lotNumber) === lotCreateData.id &&
                ((await stockDetailLot.sublot) || null) === lotCreateData.sublot
            );
        });
    }

    if (result && throwErrorIfExist) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-stock/function__stock_count_lib__line_already_exists_in_a_not_closed_stock-count',
                'The line already exists in the stock count {{number}}.',
                {
                    number: stockCountNumber,
                },
            ),
        );
    }
    return result;
}
```

---

## Part 5: Validations (Absence in this Specific Printing Flow)

It is crucial to note a significant difference between the Stock Count printing workflow and more complex document printing processes (like Sales Invoices):

-   **No Explicit Pre-Print Validations:** Within the `stock-count.ts` (page) or `stock-count.ts` (node) files, there are **no explicit, document-specific validations** performed immediately before invoking the print function. The system does not check the stock count's status (e.g., `draft`, `counted`, `closed`) or any other business rules directly within this printing flow.
-   **Reliance on Reporting Module:** Any validations related to report generation (e.g., data availability, template existence) would be handled by the generic `@sage/xtrem-reporting/PrintDocument` page itself, not by the Stock Count module.

**End Value for Testers:** A tester should understand that the Stock Count module does not enforce any specific business rules or document states before allowing a print. Printing is treated as a read-only operation that simply renders the current state of the document.

---

## Part 6: Post-Print Actions (Absence in this Specific Printing Flow)

Similar to pre-print validations, there are no explicit post-print actions directly tied to the printing event within the Stock Count module:

-   **No Flag Updates:** The `StockCount` node does not have an `isPrinted` flag, and no such flag is set after the print operation.
-   **No Financial Notifications:** The act of printing a Stock Count does not trigger any notifications to the finance system or other modules.

**End Value for Testers:** The printing of a Stock Count is a standalone, read-only operation that does not alter the state of the Stock Count document or trigger any subsequent business processes within the Stock Count module.

---

## Conclusion

The Stock Count printing workflow is a simple, client-side driven process that leverages a generic reporting module. It is primarily a utility for generating a visual representation of the stock count data. Testers should focus on verifying that the correct report is generated with the selected options, rather than looking for complex pre-flight checks or post-print state changes within the Stock Count module itself.

This document has been updated to include a detailed overview of the document integrity validations that ensure the accuracy and reliability of the Stock Count data, which is then presented in the printed report. These validations, while not directly part of the printing action, are fundamental to the trustworthiness of the printed output.