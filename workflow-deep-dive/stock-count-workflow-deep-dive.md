# Stock Count Workflow: A Deep Dive

This document provides a comprehensive, step-by-step guide to the Stock Count workflow, designed for testers, developers, and anyone needing an in-depth understanding of the process. It covers the entire lifecycle of a stock count, from its initial creation to the final posting of adjustments.

The core logic for this workflow is distributed across several key files:
-   `services/applications/xtrem-stock/lib/nodes/stock-count.ts` (Node definition, core mutations, and listeners)
-   `services/applications/xtrem-stock/lib/pages/stock-count.ts` (UI definition, user interactions, and page-specific logic)
-   `services/applications/xtrem-stock/lib/functions/stock-count-lib.ts` (Helper functions and shared business logic)
-   `services/applications/xtrem-stock/lib/enums/stock-count-status.ts` (Status definitions)

---

## Part 1: Stock Count Creation (The "Draft" Stage)

This initial phase involves setting up the stock count document and defining the scope of the count.

### 1.1 Initiation

A new stock count is typically initiated by a user through the UI (defined in `pages/stock-count.ts`). The initial status of a newly created stock count is `draft`.

### 1.2 Initial Data Entry

Users provide essential information for the stock count:

-   **`number`**: Automatically allocated by the system (`xtremMasterData.functions.allocateDocumentNumber`).
-   **`description`**: A user-defined description for the count.
-   **`stockSite`**: The inventory site where the stock count will take place. This is a mandatory field.
-   **`effectiveDate`**: The date on which the stock count is effective. Defaults to today's date.
-   **`counter`**: The name of the person performing the count.

### 1.3 Defining Filtering Criteria

Users can specify criteria to narrow down the items included in the stock count. These criteria are stored as properties on the `StockCount` node and are used to filter `itemSites`:

-   **`fromItem` / `toItem`**: Defines a range of items to be included.
-   **`locations`**: Specific locations to include in the count.
-   **`zones`**: Specific location zones to include in the count.
-   **`categories`**: Item categories to include.
-   **`lastCountDate`**: Items not counted as of this date will be included.
-   **`hasStockRecords`**: A boolean indicating whether to include only items with existing stock records or also non-stock items.

These criteria are frozen (`isFrozen() { return (await this.status) !== 'draft'; }`) once the stock count moves beyond the `draft` stage, ensuring consistency during the counting process.

---

## Part 2: Generating Count Lines (Moving to "toBeCounted")

Once the initial criteria are set, the system generates the individual lines that will be part of the stock count.

### 2.1 Action: `confirmStockCount` Mutation

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** This mutation is triggered by the user to populate the stock count with lines based on the defined filtering criteria and selected records.
-   **Input:** `selectedRecords` object, which can specify `stockRecords` (IDs of existing stock records), `itemSites` (IDs of item sites), and `allSelected` (boolean indicating if all filtered records should be included).

### 2.2 Selection Logic (`stock-count-lib.ts`)

The `confirmStockCount` mutation leverages helper functions in `stock-count-lib.ts` to determine which lines to create:

-   **`getStockRecordsToStockCountLine`**: Retrieves existing stock records that match the criteria.
-   **`getItemSiteToStockCountLine`**: Retrieves item sites that match the criteria, particularly for non-stock items or items without existing stock records.

### 2.3 Line Creation

For each selected record, a `StockCountLine` is created and appended to the `lines` collection of the `StockCount` node:

-   **`addLineToStockCount`**: Used for items with existing stock records. It populates the line with details from the `stockRecord` (item, stock status, location, lot, quantity, etc.).
-   **`addLineWithoutStockToStockCount`**: Used for non-stock items or items without existing stock records. It creates a line with a zero quantity and marks it as a non-stock item.

### 2.4 Validation: `checkStockCountLineExists`

-   **Location:** `stock-count-lib.ts`
-   **Purpose:** Before adding a new line, this function checks if the exact line (item, location, lot, stock status, etc.) already exists in another non-`closed` stock count. This prevents duplicate counting and ensures data integrity.
-   **Outcome:** If a duplicate is found, the record is excluded, and a message is logged.

### 2.5 End State

-   The `StockCount` status is updated from `draft` to `toBeCounted`.
-   The `lines` collection is populated with the generated stock count lines, each with an initial status of `toBeCounted`.

---

## Part 3: The Counting Process (Moving to "countInProgress" and "counted")

This phase involves the physical counting of stock and recording the results in the system.

### 3.1 Action: `start` Mutation

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** Initiates the active counting phase.
-   **Outcome:** The `StockCount` status changes from `toBeCounted` to `countInProgress`.
-   **Line Initialization:** For lines created from stock records, the `quantityInStockUnit` is populated from the actual stock record. If the stock record no longer exists or has zero quantity, the line status might be set to `excluded`.

### 3.2 User Interaction: Recording Counted Quantities

-   Users manually enter the `countedQuantityInStockUnit` for each line through the UI (managed by `pages/stock-count.ts`).
-   The UI provides fields for `location`, `stockStatus`, `lot`, `sublot`, `expirationDate`, and `supplierLot` based on the item's lot management settings.

### 3.3 Action: `confirmZeroQuantity` Mutation

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** Allows users to quickly confirm lines where the physical count resulted in zero quantity.
-   **Validation:** Can only be performed if the stock count is not `toBeCounted` (i.e., it has started) and not `closed`.
-   **Outcome:** For lines with `countedQuantityInStockUnit` of 0 and `toBeCounted` status, their status is updated to `counted`.

### 3.4 Automatic Status Update (`saveBegin`)

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** The system automatically updates the header status based on the line statuses.
-   **Logic:** In the `saveBegin` hook, if the stock count is not `draft` or `closed`, and all lines have a status of `excluded` or `counted`, the `StockCount` status is automatically updated to `counted`.

### 3.5 Line Statuses

Individual `StockCountLine` items can have the following statuses:

-   `toBeCounted`: The line is awaiting counting.
-   `counted`: The line has been counted.
-   `excluded`: The line has been explicitly excluded from the count (e.g., if the stock record disappeared).

---

## Part 4: Posting the Stock Count (Moving to "closed")

This is the final stage where the stock count adjustments are applied to the inventory.

### 4.1 Action: `postToStock` Mutation

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** Triggers the process of applying the counted quantities to the actual stock levels.

### 4.2 Pre-Posting Validations (`controlBeforePosting`)

Before any stock adjustments are made, a critical set of validations are performed by `stock-count-lib.ts`:

-   **Status Check (`controlStatusBeforePosting`)**: Ensures the `StockCount` status is `counted`. If not, an error is thrown, preventing posting of incomplete counts.
-   **Allocation Check (`controlAllocationsBeforePosting`)**: Verifies that for any line, the `countedQuantityInStockUnit` is not less than the `totalAllocated` quantity. This prevents negative stock issues due to existing allocations.
-   **Stock Record Check (`controlStockRecordBeforePosting`)**: Confirms that the underlying stock records for lines created from existing stock still exist and haven't changed unexpectedly since the count began. If a record is missing, the line must be excluded.

### 4.3 Stock Transaction Processing

-   **`setStockTransactionToProgress`**: The system marks the stock count as `inProgress` for stock transactions to prevent concurrent modifications.
-   **`stockCountRequestNotification`**: A notification is sent to the stock service, initiating the actual stock adjustment process. This is an asynchronous operation.

### 4.4 Stock Reply Listener (`onStockReply`)

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** This listener waits for a reply from the stock service, indicating the outcome of the stock adjustment.
-   **Outcome:** If the stock adjustment is successful and the document is completed, the `onceStockCompleted` function is called.

### 4.5 Final State Change: `onceStockCompleted`

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** Finalizes the stock count document.
-   **Outcome:** The `StockCount` status is set to `closed`.
-   **Financial Notification:** If the `doStockPosting` flag is enabled for the company (meaning stock movements impact the general ledger), a `stockCountAdjustmentNotification` is sent to the finance module. This ensures that financial records are updated to reflect the stock variances.

---

## Part 5: Financial Integration & Reposting

### 5.1 Reposting (`repost` Mutation)

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** Allows users to re-attempt posting to the finance module if the initial integration failed.
-   **Validation:** Can only be executed if the `financeIntegrationStatus` is `'Failed'` or `'Not recorded'`.

### 5.2 Finance Reply Listener (`onFinanceIntegrationReply`)

-   **Location:** `nodes/stock-count.ts`
-   **Purpose:** This listener updates the `financeIntegrationStatus` of the stock count based on the response received from the finance system after a posting attempt.

---

This deep dive provides a comprehensive overview of the Stock Count workflow, detailing the various stages, validations, and system interactions. Testers should use this document to verify each step, ensuring data integrity and correct system behavior throughout the process.