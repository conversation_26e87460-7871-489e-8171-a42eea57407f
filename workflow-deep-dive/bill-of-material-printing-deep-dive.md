# Bill of Material Printing Workflow: A Deep Dive

This document provides a detailed analysis of the Bill of Material (BOM) printing workflow, encompassing both the general document integrity validations that ensure a BOM's correctness and the specific controls within the printing process itself. It is designed for testers and developers seeking a comprehensive understanding.

**Key Files Involved:**
-   `services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts` (Core BOM data model and mutations)
-   `services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts` (Centralized BOM validation logic)
-   `services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts` (Transient node for print data preparation and report generation)
-   `services/shared/xtrem-technical-data/lib/pages/bill-of-material-printout-panel.ts` (UI for print options)

---

## Part 1: BOM Document Integrity Validations

These validations ensure the accuracy and consistency of the Bill of Material document throughout its lifecycle. While not directly part of the printing action, a valid BOM is a prerequisite for a meaningful printout.

### 1.1 Item and Site Validation

-   **Description:** Ensures that the manufactured `item` associated with the BOM is active and that the `site` is a manufacturing site.
-   **Purpose/End Value:** Guarantees that BOMs are created for valid and operational manufacturing contexts.
-   **Location:** `services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts` (within `item` and `site` property decorators)

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts
    @decorators.referenceProperty<BillOfMaterial, 'item'>({ // ...
        filters: { control: { isManufactured: true, isStockManaged: true } },
        async control(cx, item) {
            await xtremMasterData.functions.controls.item.inactiveItemControl(cx, item);
        },
        // ...
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<BillOfMaterial, 'site'>({ // ...
        filters: { control: { isManufacturing: true } },
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        // ...
    })
    readonly site: Reference<xtremSystem.nodes.Site>;
```

### 1.2 Base Quantity Validation

-   **Description:** Ensures that the `baseQuantity` (the quantity of the manufactured item produced by this BOM) is a positive value.
-   **Purpose/End Value:** Prevents the creation of illogical BOMs that would produce zero or negative quantities.
-   **Location:** `services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts` (within `baseQuantity` property decorator)

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts
    @decorators.decimalProperty<BillOfMaterial, 'baseQuantity'>({ // ...
        async control(cx, val) {
            await cx.error.if(val).is.not.greater.than(0);
        },
        // ...
    })
    readonly baseQuantity: Promise<decimal>;
```

### 1.3 Item-Site Mandatory for 'Available to Use' Status

-   **Description:** If a BOM's `status` is set to `availableToUse`, it must have an associated `itemSite` record.
-   **Purpose/End Value:** Ensures that a BOM can only be made available for use in manufacturing if it's properly linked to an item and site, which is essential for production planning and execution.
-   **Location:** `services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts` (called from `BillOfMaterial.status` property control)

```typescript
// From services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts
export async function validateItemSiteForAvailableToUseBom(
    cx: ValidationContext,
    billOfMaterial: xtremTechnicalData.nodes.BillOfMaterial,
) {
    if ((await billOfMaterial.status) === 'availableToUse' && !(await billOfMaterial.itemSite))
        cx.error.addLocalized(
            '@sage/xtrem-technical-data/nodes__bill_of_material__item_site_mandatory_for_available_to_use',
            'You need to add an item-site record before you can set the status to Available to use.',
        );
}
```

### 1.4 BOM Status Change Validation

-   **Description:** Controls the permitted transitions between BOM statuses (`inDevelopment`, `availableToUse`, `suspended`). For example, a BOM cannot directly transition from `suspended` to `inDevelopment`.
-   **Purpose/End Value:** Enforces a controlled lifecycle for BOMs, ensuring that status changes adhere to predefined business rules and maintain data integrity.
-   **Location:** `services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts` (called from `BillOfMaterial.status` property control if BOM revisions are managed)

```typescript
// From services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts
export async function validateBomStatusChange(
    cx: ValidationContext,
    bomRevision: xtremTechnicalData.nodes.BillOfMaterial | xtremTechnicalData.nodes.BillOfMaterialRevision,
) {
    if (bomRevision.$.status === NodeStatus.added) return;
    const newStatus = await bomRevision.status;
    const oldStatus = await (await bomRevision.$.old).status;

    if (oldStatus === newStatus) return;
    // Define the permitted status transitions
    const permittedTransitions: Record<xtremTechnicalData.enums.Availability, xtremTechnicalData.enums.Availability[]> =
        {
            inDevelopment: ['availableToUse'],
            availableToUse: ['suspended'],
            suspended: ['availableToUse'],
        };

    // Check if the new status is a valid transition from the current status
    const validTransitions = permittedTransitions[oldStatus] || [];
    if (!validTransitions.includes(newStatus)) {
        cx.error.addLocalized(
            '@sage/xtrem-technical-data/nodes__bill_of_material__status_change_not_permitted',
            `You cannot change the status from the old status: {{oldStatus}}, to the new status: {{newStatus}}.`,
            { oldStatus, newStatus },
        );
    }
}
```

### 1.5 BOM Deletion Control

-   **Description:** Prevents the deletion of a BOM if it has associated revisions.
-   **Purpose/End Value:** Ensures that historical BOM data is preserved, especially when revisions are managed, maintaining data integrity and auditability. This prevents accidental loss of critical manufacturing history.
-   **Location:** `services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts` (called from `BillOfMaterial.controlDelete`)

```typescript
// From services/shared/xtrem-technical-data/lib/events/control/bill-of-material-control.ts
export async function checkBomCanBeDeleted(
    cx: ValidationContext,
    billOfMaterial: xtremTechnicalData.nodes.BillOfMaterial,
) {
    if ((await billOfMaterial.revisions.length) > 0) {
        cx.error.addLocalized(
            '@sage/xtrem-technical-data/nodes__bill_of_material__cannot_delete_bom_with_revision',
            'This BOM has a revision. You cannot delete the BOM.',
        );
    }
}
```

### 1.6 Circular BOM Reference Check

-   **Description:** This is a critical validation that checks for circular references within BOM structures (e.g., if Item A requires Item B, and Item B, in turn, requires Item A). This can be run on a single BOM or all BOMs.
-   **Purpose/End Value:** Prevents infinite loops in manufacturing planning and costing, ensuring the BOM structure is logically sound and manufacturable. A circular reference would make it impossible to build the product.
-   **Location:** `services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts` (`checkForCircularBOM` mutation) and `xtremTechnicalData.functions.bomLib.checkForCircularBom` (implementation)

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts
    @decorators.asyncMutation<typeof BillOfMaterial, 'checkForCircularBOM'>({
        isPublished: true,
        startsReadOnly: true,
        isSchedulable: true,
        parameters: [
            { name: 'bomId', type: 'string', isMandatory: false },
        ],
        return: { type: 'string' },
    })
    static async checkForCircularBOM(context: Context, bomId: string) {
        // ... (logic to get BOMs to check)
        await asyncArray(bomIds).forEach(async checkingBomId => {
            const circularBom = await xtremTechnicalData.functions.bomLib.checkForCircularBom(context, checkingBomId);
            if (circularBom) {
                // ... (error logging)
            }
        });
        // ... (progress updates and final logging)
    }
```

---

## Part 2: Bill of Material Printing Workflow (Refactored)

The BOM printing process is designed to generate various reports based on the BOM structure and user-selected options.

### 2.1 Entry Point: `bulkPrint` Mutation

-   **Location:** `services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts`
-   **Trigger:** Initiated by a user action (e.g., clicking a print button on a BOM list or detail page). This is a bulk mutation, allowing multiple BOMs to be processed.
-   **Parameters:** `multilevel` (boolean), `cost` (boolean), `instruction` (boolean) to control report content.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts
    @decorators.bulkMutation<typeof BillOfMaterial, 'bulkPrint'>({
        isPublished: true,
        queue: 'reporting',
        parameters: [
            { name: 'multilevel', type: 'boolean', isMandatory: false },
            { name: 'cost', type: 'boolean', isMandatory: false },
            { name: 'instruction', type: 'boolean', isMandatory: false },
        ],
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-technical-data/node__bill_of_material_bulk_print_report_name',
                'Bill of material',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'bomMultiLevel',
                documents: reports,
                reportName,
            });
        },
    })
    static async bulkPrint(
        context: Context,
        bom: BillOfMaterial,
        multilevel = true,
        cost = false,
        instruction = false,
    ) {
        const report = await xtremTechnicalData.nodes.BillOfMaterialPrintout.generateBomReport(
            context,
            bom._id.toString(),
            !multilevel,
            !cost,
            !instruction,
        );
        if (report !== null) {
            return report;
        }
        throw new Error('No report');
    }
```

### 2.2 Print Options Panel

-   **Location:** `services/shared/xtrem-technical-data/lib/pages/bill-of-material-printout-panel.ts`
-   **Purpose:** This transient UI page is displayed as a dialog to collect user preferences for the BOM report.
-   **Fields:** Checkboxes for `multilevel` (to include sub-BOMs), `cost` (to include standard costs), and `instruction` (to include manufacturing instructions).

```typescript
// From services/shared/xtrem-technical-data/lib/pages/bill-of-material-printout-panel.ts
@ui.decorators.page<BillOfMaterialPrintoutPanel>({
    title: 'Bill of material report options',
    module: 'technical-data',
    mode: 'default',
    isTransient: true,
    businessActions() {
        return [this.$standardCancelAction, this.$standardDialogConfirmationAction];
    },
    onLoad() {
        this.multilevel.value = true;
        this.cost.value = false;
        this.instruction.value = false;
    },
})
export class BillOfMaterialPrintoutPanel extends ui.Page<GraphApi> {
    // ... (section and block decorators)

    @ui.decorators.checkboxField<BillOfMaterialPrintoutPanel>({
        parent() { return this.block; },
        title: 'Multilevel',
    })
    multilevel: ui.fields.Checkbox;

    @ui.decorators.checkboxField<BillOfMaterialPrintoutPanel>({
        parent() { return this.block; },
        title: 'Standard cost',
    })
    cost: ui.fields.Checkbox;

    @ui.decorators.checkboxField<BillOfMaterialPrintoutPanel>({
        parent() { return this.block; },
        title: 'Instructions',
    })
    instruction: ui.fields.Checkbox;
}
```

### 2.3 Data Preparation for Report (`BillOfMaterialPrintout.generateBomReport`)

-   **Location:** `services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts`
-   **Purpose:** This `asyncMutation` is responsible for orchestrating the data gathering and report generation.
-   **Process:**
    1.  Reads the target `BillOfMaterial` node.
    2.  Creates a transient `BillOfMaterialPrintout` node (`BillOfMaterialPrintout.createBomPrintout`). This transient node is populated with the BOM structure, potentially traversing multiple levels of components.
    3.  The `bomMultiLevelComponents` function recursively gathers all components, their levels, and required quantities based on the `multilevel` option.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts
    static async generateBomReport(
        context: Context,
        bomId: string,
        singleLevel = false,
        omitCost = false,
        omitInstruction = false,
        skipReport = false,
        skipDelete = false,
        baseQuantity?: decimal,
    ): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile> | null> {
        // ... (logging and reading BOM)

        const bomPrintout = await BillOfMaterialPrintout.createBomPrintout(
            context,
            bom,
            singleLevel,
            baseQuantity || (await bom.baseQuantity),
        );

        // ... (report generation and cleanup)
    }

    private static async bomMultiLevelComponents(
        context: Context,
        bom: xtremTechnicalData.nodes.BillOfMaterial,
        baseQuantity: decimal,
        singleLevel: boolean,
        level?: integer,
    ): Promise<Array<ComponentWithLevel>> {
        // ... (component traversal logic)
    }
```

### 2.4 Printing-Specific Validation: Stop Requested

-   **Description:** During the recursive traversal of BOM components (`bomMultiLevelComponents`), the system checks if a stop has been requested for the batch process.
-   **Purpose/End Value:** Allows for graceful termination of long-running report generation processes, preventing resource exhaustion or unwanted continued execution if the user or system signals a stop.
-   **Location:** `services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts` (within `bomMultiLevelComponents`)

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts (within bomMultiLevelComponents)
                if (await context.batch.isStopRequested()) {
                    await context.batch.logMessage('warning', 'Stop requested');
                    await context.batch.confirmStop();
                    throw Error(
                        context.localize(
                            '@sage/xtrem-technical-data/nodes__bill_of_material_printout__printout_stopped',
                            'BOM printout stopped.',
                        ),
                    );
                }
```

### 2.5 PDF Generation

-   **Description:** After the `BillOfMaterialPrintout` node is populated with the necessary data, the actual PDF report is generated.
-   **Action:** The `xtremReporting.nodes.Report.generateReportPdf` function is called.
-   **Parameters:** It receives the report name (`'bomMultiLevel'`) and variables derived from the print options (e.g., `bomPrintoutId`, `singleLevel`, `omitCost`, `omitInstruction`).
-   **End Value:** A PDF document representing the BOM report is created and typically returned to the client for display or download.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts (within generateBomReport)
        let report = null;
        if (!skipReport) {
            report = await xtremReporting.nodes.Report.generateReportPdf(context, 'bomMultiLevel', '', {
                variables: JSON.stringify({
                    bomPrintoutId: await bomPrintout.id,
                    singleLevel: singleLevel ? 'true' : 'false',
                    omitCost: omitCost ? 'true' : 'false',
                    omitInstruction: omitInstruction ? 'true' : 'false',
                }),
            });
            await context.batch.logMessage('result', 'BOM printout data reported', { data: report });
        }
```

### 2.6 Cleanup

-   **Description:** The transient `BillOfMaterialPrintout` node, which was created solely for report data preparation, is deleted after the report is generated.
-   **Purpose/End Value:** Ensures that temporary data is not persisted, maintaining system efficiency and cleanliness.

```typescript
// From services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts (within generateBomReport)
        if (!skipDelete) {
            await bomPrintout.$.delete();
            await context.batch.logMessage('info', 'BOM printout data deleted');
        }
```

---

## Part 3: Post-Print Actions (Absence)

-   **No Direct Post-Print Actions:** Within the BOM module, there are no explicit post-print actions directly tied to the printing event.
-   **No Flag Updates:** The `BillOfMaterial` node does not have an `isPrinted` flag, and no such flag is set after the print operation.
-   **No Financial Notifications:** The act of printing a BOM does not trigger any notifications to the finance system or other modules.

**End Value for Testers:** The printing of a BOM is a standalone, read-only operation that does not alter the state of the Bill of Material document or trigger any subsequent business processes within the BOM module.

---

## Conclusion

The Bill of Material printing workflow is a robust process that combines document integrity validations with a flexible reporting mechanism. While the direct printing action itself has minimal pre-print controls, the underlying BOM document is subject to rigorous validations throughout its lifecycle, ensuring the accuracy and reliability of the data presented in the printed report.

This document has provided a deep dive into both the document-level validations and the specific steps and controls involved in generating a BOM printout. Testers should use this information to verify the correctness of BOM data and the proper functioning of the printing process, including the handling of various report options and batch process interruptions.